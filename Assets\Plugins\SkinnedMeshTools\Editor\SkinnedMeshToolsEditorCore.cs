#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using SkinnedMeshTools;

[InitializeOnLoad]
public class SkinnedMeshToolsEditorCore
{
    // Tool modes
    public enum ToolMode
    {
        View,
        Rotate,
        Move,
        Scale
    }

    // Handle types
    public enum HandleType
    {
        Position,
        Rotation,
        Scale
    }

    static private Texture circleTexture => Resources.Load<Texture>("Circle_Icon");
    static private Texture boneTexture => Resources.Load<Texture>("Circle_Icon"); // We'll create a better bone icon later

    static private Material _boneWeightMaterial;
    static private Material boneWeightMaterial
    {
        get
        {
            if (_boneWeightMaterial == null)
            {
                var shader = Shader.Find("Hidden/SkinnedMeshTools/VertexColorShader");
                if (shader != null)
                    _boneWeightMaterial = new Material(shader);
            }
            return _boneWeightMaterial;
        }
    }

    static private GameObject _previousSelected;
    static private SkinnedMeshRenderer _currentSkinnedMesh;
    static private Transform _selectedBone;
    static private ToolMode _currentToolMode = ToolMode.View;
    static private bool _isDragging = false;
    static private Vector3 _dragStartPosition;
    static private Quaternion _dragStartRotation;
    static private Vector3 _dragStartScale;

    // UI Style cache
    static private GUIStyle _headerStyle;
    static private GUIStyle _buttonStyle;
    static private GUIStyle _toggleStyle;

    private static SkinnedMeshToolsEditorConfig _config;
    static public SkinnedMeshToolsEditorConfig Config
    {
        get
        {
            if (_config == null)
            {
                CreateConfig();
            }
            return _config;
        }
        private set
        {
            _config = value;
        }
    }

    static void CreateConfig()
    {
        try
        {
            _config = (SkinnedMeshToolsEditorConfig) AssetDatabase.LoadAssetAtPath("Assets/Resources/SkinnedMeshToolsEditorConfig.asset",
                typeof(SkinnedMeshToolsEditorConfig));

            if (_config == null)
            {
                _config = ScriptableObject.CreateInstance<SkinnedMeshToolsEditorConfig>();
                _config.boneWeightColor = new Color(1, 0, 0, 0);

                if (!AssetDatabase.IsValidFolder("Assets/Resources"))
                {
                    AssetDatabase.CreateFolder("Assets","Resources");
                }
                AssetDatabase.CreateAsset(_config, "Assets/Resources/SkinnedMeshToolsEditorConfig.asset");
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
        }
        catch (System.Exception e)
        {
            UnityEngine.Debug.LogWarning($"Failed to create SkinnedMeshTools config: {e.Message}. Using default settings.");
            _config = ScriptableObject.CreateInstance<SkinnedMeshToolsEditorConfig>();
            _config.boneWeightColor = new Color(1, 0, 0, 0);
        }
    }
    
    static SkinnedMeshToolsEditorCore()
    {
        SceneView.duringSceneGui -= OnSceneGUI;
        SceneView.duringSceneGui += OnSceneGUI;

        Selection.selectionChanged -= OnSelectionChanged;
        Selection.selectionChanged += OnSelectionChanged;
    }

    static void InitializeStyles()
    {
        if (_headerStyle == null)
        {
            _headerStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                normal = { textColor = Color.white }
            };
        }

        if (_buttonStyle == null)
        {
            _buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 10,
                padding = new RectOffset(8, 8, 4, 4)
            };
        }

        if (_toggleStyle == null)
        {
            _toggleStyle = new GUIStyle(GUI.skin.toggle)
            {
                fontSize = 10,
                normal = { textColor = Color.white },
                onNormal = { textColor = Color.white }
            };
        }
    }

    static void OnSelectionChanged()
    {
        // Check if the selected object is a bone in the current skinned mesh
        GameObject selectedObject = Selection.activeGameObject;
        bool isBoneSelected = false;

        if (selectedObject != null && _currentSkinnedMesh != null && _currentSkinnedMesh.bones != null)
        {
            Transform selectedTransform = selectedObject.transform;
            for (int i = 0; i < _currentSkinnedMesh.bones.Length; i++)
            {
                if (_currentSkinnedMesh.bones[i] == selectedTransform)
                {
                    _selectedBone = selectedTransform;
                    Config.boneIndex = i;
                    isBoneSelected = true;
                    break;
                }
            }
        }

        // Only clear bone selection if a non-bone object was selected
        if (!isBoneSelected)
        {
            _selectedBone = null;
            // DON'T reset tool mode - preserve user's current tool selection
            // _currentToolMode = ToolMode.View; // REMOVED - this was causing tool mode to reset when selecting bones
        }

        SceneView.RepaintAll();
    }

    private static void OnSceneGUI(SceneView p_view)
    {
        SkinnedMeshToolsPerformanceMonitor.StartTimer("UIRendering");

        if (Config == null || !Config.enabled)
        {
            Tools.hidden = false;
            SkinnedMeshToolsPerformanceMonitor.StopTimer("UIRendering");
            return;
        }

        GameObject selected = Selection.activeGameObject;
        if (selected == null || !selected.activeInHierarchy)
        {
            Tools.hidden = false;
            return;
        }

        _currentSkinnedMesh = selected.GetComponent<SkinnedMeshRenderer>();
        if (_currentSkinnedMesh == null)
        {
            // Check if selected object is a bone (child of skinned mesh)
            _currentSkinnedMesh = selected.GetComponentInParent<SkinnedMeshRenderer>();
            if (_currentSkinnedMesh == null)
            {
                // Try children as fallback
                _currentSkinnedMesh = selected.GetComponentInChildren<SkinnedMeshRenderer>();
                if (_currentSkinnedMesh == null)
                {
                    Tools.hidden = false;
                    return;
                }
            }
        }

        // Handle root bone selection when main SkinnedMesh GameObject is selected
        if (_currentSkinnedMesh != null && selected == _currentSkinnedMesh.gameObject)
        {
            // If main SkinnedMesh object is selected and we have bones, auto-select root bone
            if (_currentSkinnedMesh.bones != null && _currentSkinnedMesh.bones.Length > 0 && _currentSkinnedMesh.bones[0] != null)
            {
                if (_selectedBone == null || _currentToolMode != ToolMode.View)
                {
                    _selectedBone = _currentSkinnedMesh.bones[0]; // Select root bone
                    Config.boneIndex = 0;
                }
            }
        }

        // Handle tool hiding based on mode
        Tools.hidden = _currentToolMode != ToolMode.View;

        // Draw bone weights visualization
        if (Config.showBones && Config.showBoneWeights && Config.boneIndex >= 0 && Config.boneIndex < _currentSkinnedMesh.bones.Length)
            DrawBoneWeights(Config.boneIndex);

        // Store event state before UI drawing
        Event currentEvent = Event.current;
        EventType originalEventType = currentEvent.type;

        // Draw UI overlay FIRST to ensure UI responsiveness
        DrawGUI();

        // Check if UI consumed the event by comparing event state
        bool uiConsumedEvent = (currentEvent.type == EventType.Used && originalEventType != EventType.Used);

        // Draw bones and handles AFTER UI but allow them to handle events
        if (Config.showBones)
            DrawBones();

        // Draw bone influence gizmos when showing weights
        if (Config.showBones && Config.showBoneWeights)
        {
            AutomaticWeightSystem.DrawBoneInfluenceGizmos(_currentSkinnedMesh);
        }

        // Handle other input events only if UI didn't consume them
        if (!uiConsumedEvent)
        {
            HandleInput();
        }

        // Force repaint if we're in an active tool mode
        if (_currentToolMode != ToolMode.View)
            p_view.Repaint();

        SkinnedMeshToolsPerformanceMonitor.StopTimer("UIRendering");
    }

    private static void DrawGUI()
    {
        // Initialize styles if needed
        InitializeStyles();

        Handles.BeginGUI();

        // Create a proper background with transparency
        var backgroundStyle = new GUIStyle(GUI.skin.box);
        var backgroundTexture = new Texture2D(1, 1);
        backgroundTexture.SetPixel(0, 0, new Color(0.2f, 0.2f, 0.2f, 0.8f));
        backgroundTexture.Apply();
        backgroundStyle.normal.background = backgroundTexture;

        // Main toolbar
        var toolbarRect = new Rect(10, 10, Screen.width - 20, 120);
        GUI.Box(toolbarRect, "", backgroundStyle);

        GUILayout.BeginArea(new Rect(15, 15, Screen.width - 30, 110));

        // Header
        GUILayout.Label("SkinnedMesh Tools v2.0", _headerStyle);
        GUILayout.Space(5);

        // Main controls row
        GUILayout.BeginHorizontal();
        Config.showBones = GUILayout.Toggle(Config.showBones, "Show Bones", _toggleStyle, GUILayout.Width(100));

        if (Config.showBones)
        {
            GUILayout.Space(10);
            Config.showBoneWeights = GUILayout.Toggle(Config.showBoneWeights, "Show Influence", _toggleStyle, GUILayout.Width(120));

            if (Config.showBoneWeights && _currentSkinnedMesh != null && _currentSkinnedMesh.bones.Length > 0)
            {
                GUILayout.Space(10);
                string[] boneNames = GetBoneNames(_currentSkinnedMesh);
                Config.boneIndex = Mathf.Clamp(Config.boneIndex, 0, boneNames.Length - 1);

                EditorGUI.BeginChangeCheck();
                Config.boneIndex = EditorGUILayout.Popup(Config.boneIndex, boneNames, GUILayout.Width(150));
                if (EditorGUI.EndChangeCheck())
                {
                    // Sync selected bone when dropdown selection changes
                    if (Config.boneIndex >= 0 && Config.boneIndex < _currentSkinnedMesh.bones.Length)
                    {
                        _selectedBone = _currentSkinnedMesh.bones[Config.boneIndex];
                    }
                }

                // Bone radius control for selected bone
                if (_selectedBone != null)
                {
                    GUILayout.Space(10);
                    GUILayout.Label("Radius:", GUILayout.Width(50));

                    EditorGUI.BeginChangeCheck();
                    float currentRadius = AutomaticWeightSystem.GetBoneRadius(_selectedBone);

                    // Use exponential slider for better control at small values
                    float sliderValue = AutomaticWeightSystem.RadiusToSlider(currentRadius);
                    GUIContent sliderContent = new GUIContent("", "Exponential radius slider (0.01 to 10.0). Left = smaller radius, Right = larger radius");
                    float newSliderValue = GUILayout.HorizontalSlider(sliderValue, 0f, 1f, GUILayout.Width(80));
                    float newRadius = AutomaticWeightSystem.SliderToRadius(newSliderValue);

                    // Also show numeric field for precise input
                    newRadius = EditorGUILayout.FloatField(newRadius, GUILayout.Width(60));

                    if (EditorGUI.EndChangeCheck())
                    {
                        AutomaticWeightSystem.SetBoneRadius(_selectedBone, newRadius);
                        if (Config.autoRecalculateWeights)
                        {
                            AutomaticWeightSystem.RecalculateWeights(_currentSkinnedMesh);
                        }
                    }
                }
            }
        }
        GUILayout.EndHorizontal();

        // Tool mode selection
        if (Config.showBones)
        {
            GUILayout.Space(10);
            GUILayout.BeginHorizontal();
            GUILayout.Label("Tool Mode:", GUILayout.Width(70));

            // Use proper button logic instead of toggle buttons to prevent UI freezing
            GUI.backgroundColor = _currentToolMode == ToolMode.View ? Color.cyan : Color.white;
            if (GUILayout.Button("View", _buttonStyle, GUILayout.Width(60)))
                _currentToolMode = ToolMode.View;

            GUI.backgroundColor = _currentToolMode == ToolMode.Move ? Color.cyan : Color.white;
            if (GUILayout.Button("Move", _buttonStyle, GUILayout.Width(60)))
                _currentToolMode = ToolMode.Move;

            GUI.backgroundColor = _currentToolMode == ToolMode.Rotate ? Color.cyan : Color.white;
            if (GUILayout.Button("Rotate", _buttonStyle, GUILayout.Width(60)))
                _currentToolMode = ToolMode.Rotate;

            GUI.backgroundColor = _currentToolMode == ToolMode.Scale ? Color.cyan : Color.white;
            if (GUILayout.Button("Scale", _buttonStyle, GUILayout.Width(60)))
                _currentToolMode = ToolMode.Scale;

            GUI.backgroundColor = Color.white;

            // Auto-recalculate toggle and manual recalculate button
            GUILayout.Space(20);

            // Auto recalculate toggle
            EditorGUI.BeginChangeCheck();
            GUIContent toggleContent = new GUIContent("Live Weights", "When enabled, weights automatically update when bones are moved/rotated/scaled");
            Config.autoRecalculateWeights = GUILayout.Toggle(Config.autoRecalculateWeights, toggleContent, _toggleStyle, GUILayout.Width(100));
            if (EditorGUI.EndChangeCheck())
            {
                // Save the setting
                EditorUtility.SetDirty(Config);
            }

            GUILayout.Space(10);

            // Manual recalculate button
            GUI.backgroundColor = Config.autoRecalculateWeights ? Color.gray : Color.green;
            if (GUILayout.Button("Recalculate", _buttonStyle, GUILayout.Width(90)))
            {
                if (_currentSkinnedMesh != null)
                {
                    AutomaticWeightSystem.RecalculateWeights(_currentSkinnedMesh);
                }
            }
            GUI.backgroundColor = Color.white;
            GUILayout.EndHorizontal();
        }

        // Selected bone info
        if (_selectedBone != null)
        {
            GUILayout.Space(5);
            GUILayout.Label($"Selected: {_selectedBone.name}", EditorStyles.miniLabel);
        }

        GUILayout.EndArea();

        // Version label
        var versionStyle = new GUIStyle(EditorStyles.miniLabel);
        versionStyle.normal.textColor = new Color(0.7f, 0.7f, 0.7f);
        GUI.Label(new Rect(Screen.width - 150, Screen.height - 25, 150, 20), "SkinnedMeshTools v2.0", versionStyle);

        Handles.EndGUI();
    }
    
    private static void DrawBindPose()
    {
        if (_currentSkinnedMesh?.sharedMesh == null) return;

        Handles.BeginGUI();
        GUI.color = Color.green;

        List<Matrix4x4> bindPoses = new List<Matrix4x4>();
        _currentSkinnedMesh.sharedMesh.GetBindposes(bindPoses);

        for (int i = 0; i < bindPoses.Count; i++)
        {
            Matrix4x4 pose = bindPoses[i];
            // Transform bind pose position to world space
            Vector3 worldPosition = _currentSkinnedMesh.transform.TransformPoint(pose.inverse.MultiplyPoint(Vector3.zero));
            DrawPoint(worldPosition, Color.green, 1);
        }

        GUI.color = Color.white;
        Handles.EndGUI();
    }
    
    private static void DrawBones()
    {
        SkinnedMeshToolsPerformanceMonitor.StartTimer("BoneVisualization");

        List<Transform> bones = _currentSkinnedMesh.bones.ToList();
        if (bones == null || bones.Count == 0)
        {
            SkinnedMeshToolsPerformanceMonitor.StopTimer("BoneVisualization");
            return;
        }

        // Performance optimization: LOD based on distance
        Camera sceneCamera = SceneView.currentDrawingSceneView?.camera;
        if (sceneCamera == null) return;

        float distanceToMesh = Vector3.Distance(sceneCamera.transform.position, _currentSkinnedMesh.bounds.center);
        bool useLOD = Config.useLODForBones && distanceToMesh > Config.lodDistance;

        // Limit visible bones for performance
        int maxBones = useLOD ? Mathf.Min(Config.maxVisibleBones / 2, bones.Count) : Mathf.Min(Config.maxVisibleBones, bones.Count);

        // Draw bone connections
        if (Config.showBoneConnections)
        {
            Handles.color = new Color(0.8f, 0.8f, 0.8f, useLOD ? 0.3f : 0.6f);
            for (int i = 0; i < maxBones; i++)
            {
                Transform bone = bones[i];
                if (bone != null && bone.parent != null && bones.Contains(bone.parent))
                {
                    Handles.DrawLine(bone.parent.position, bone.position);
                }
            }
        }

        // Draw bone handles
        Handles.BeginGUI();
        for (int i = 0; i < maxBones; i++)
        {
            if (bones[i] != null)
                DrawBone(i, bones[i], useLOD);
        }
        Handles.EndGUI();

        // Draw transformation handles for selected bone
        if (_selectedBone != null && _currentToolMode != ToolMode.View)
        {
            DrawTransformationHandles(_selectedBone);
        }
        // Note: Transform handles only appear when a bone is selected and tool mode is not View

        SkinnedMeshToolsPerformanceMonitor.StopTimer("BoneVisualization");
    }

    private static void DrawTransformationHandles(Transform bone)
    {
        if (bone == null) return;

        // Draw transformation handles for the selected bone

        switch (_currentToolMode)
        {
            case ToolMode.Move:
                EditorGUI.BeginChangeCheck();
                Vector3 newPosition = Handles.PositionHandle(bone.position, bone.rotation);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(bone, "Move Bone");
                    bone.position = newPosition;
                    // Auto-recalculate weights when bone is moved (if enabled)
                    if (_currentSkinnedMesh != null && Config.autoRecalculateWeights)
                        AutomaticWeightSystem.RecalculateWeights(_currentSkinnedMesh);
                }
                break;

            case ToolMode.Rotate:
                EditorGUI.BeginChangeCheck();
                Quaternion newRotation = Handles.RotationHandle(bone.rotation, bone.position);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(bone, "Rotate Bone");
                    bone.rotation = newRotation;
                    // Auto-recalculate weights when bone is rotated (if enabled)
                    if (_currentSkinnedMesh != null && Config.autoRecalculateWeights)
                        AutomaticWeightSystem.RecalculateWeights(_currentSkinnedMesh);
                }
                break;

            case ToolMode.Scale:
                EditorGUI.BeginChangeCheck();
                Vector3 newScale = Handles.ScaleHandle(bone.localScale, bone.position, bone.rotation, HandleUtility.GetHandleSize(bone.position));
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(bone, "Scale Bone");
                    bone.localScale = newScale;
                    // Auto-recalculate weights when bone is scaled (if enabled)
                    if (_currentSkinnedMesh != null && Config.autoRecalculateWeights)
                        AutomaticWeightSystem.RecalculateWeights(_currentSkinnedMesh);
                }
                break;
        }
    }

    private static void DrawBone(int p_index, Transform bone, bool useLOD = false)
    {
        if (bone == null) return;

        // Check if bone is behind the camera or too far
        Camera sceneCamera = SceneView.currentDrawingSceneView?.camera;
        if (sceneCamera != null)
        {
            Vector3 viewportPoint = sceneCamera.WorldToViewportPoint(bone.position);
            // Skip bones that are behind the camera or outside reasonable bounds
            if (viewportPoint.z < 0 || viewportPoint.x < -0.5f || viewportPoint.x > 1.5f ||
                viewportPoint.y < -0.5f || viewportPoint.y > 1.5f)
            {
                return;
            }
        }

        Vector2 pos2D = HandleUtility.WorldToGUIPoint(bone.position);
        int baseScale = Mathf.RoundToInt(12 * Config.boneHandleSize);
        int scale = useLOD ? baseScale / 2 : baseScale;
        Rect mouseRect = new Rect(pos2D.x - scale / 2, pos2D.y - scale / 2, scale, scale);

        // Determine bone color and scale based on state
        Color boneColor = Color.white;
        float alpha = 1.0f;

        // Adjust alpha based on distance from camera for depth perception
        if (sceneCamera != null)
        {
            float distance = Vector3.Distance(sceneCamera.transform.position, bone.position);
            float maxDistance = 50.0f; // Adjust based on your scene scale
            alpha = Mathf.Clamp01(1.0f - (distance / maxDistance) * 0.5f);
        }

        if (_selectedBone == bone)
        {
            boneColor = Color.cyan;
            scale = useLOD ? baseScale : Mathf.RoundToInt(baseScale * 1.5f);
            alpha = 1.0f; // Selected bones are always fully visible
        }
        else if (Config.showBoneWeights && p_index == Config.boneIndex)
        {
            boneColor = Color.green;
            scale = useLOD ? baseScale : Mathf.RoundToInt(baseScale * 1.3f);
        }
        else if (mouseRect.Contains(Event.current.mousePosition))
        {
            boneColor = Color.yellow;
            scale = useLOD ? baseScale : Mathf.RoundToInt(baseScale * 1.2f);
        }

        boneColor.a = alpha;
        GUI.color = boneColor;

        // Draw bone button
        if (GUI.Button(new Rect(pos2D.x - scale / 2, pos2D.y - scale / 2, scale, scale), boneTexture, GUIStyle.none))
        {
            SelectBone(bone); // Use the proper selection method
        }

        // Draw bone name on hover (only if not using LOD and setting is enabled)
        if (!useLOD && Config.showBoneNames && mouseRect.Contains(Event.current.mousePosition))
        {
            var labelStyle = new GUIStyle(EditorStyles.miniLabel);
            labelStyle.normal.textColor = Color.white;
            labelStyle.normal.background = Texture2D.blackTexture;
            labelStyle.padding = new RectOffset(4, 4, 2, 2);

            var content = new GUIContent(bone.name);
            var labelSize = labelStyle.CalcSize(content);
            var labelRect = new Rect(pos2D.x - labelSize.x / 2, pos2D.y - scale / 2 - labelSize.y - 5, labelSize.x, labelSize.y);

            GUI.Label(labelRect, content, labelStyle);
        }

        GUI.color = Color.white;
    }

    private static void HandleInput()
    {
        Event e = Event.current;

        // Keyboard shortcuts disabled to prevent interference with Unity camera controls
        // Users can use the UI buttons or EditorWindow to change tool modes instead

        // Only handle Escape key for deselecting bones (less likely to interfere)
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Escape)
        {
            _selectedBone = null;
            // Keep current tool mode - user may want to select another bone and continue with the same tool
            // _currentToolMode = ToolMode.View; // REMOVED - preserve tool mode when deselecting
            e.Use();
        }
    }

    private static void DrawPoint(Vector3 p_position, Color p_color, float p_scale)
    {
        GUI.color = p_color;
        Vector2 pos2D = HandleUtility.WorldToGUIPoint(p_position);
        GUI.DrawTexture(new Rect(pos2D.x - 4*p_scale, pos2D.y - 4*p_scale, 8*p_scale, 8*p_scale), circleTexture);
        GUI.color = Color.white;
    }
    
    private static void DrawBoneWeights(int p_boneIndex)
    {
        if (boneWeightMaterial == null || _currentSkinnedMesh == null || _currentSkinnedMesh.sharedMesh == null)
            return;

        try
        {
            Mesh mesh = GenerateBoneWeightMesh(_currentSkinnedMesh, p_boneIndex);
            if (mesh != null)
            {
                boneWeightMaterial.SetPass(0);
                Graphics.DrawMeshNow(mesh, _currentSkinnedMesh.transform.localToWorldMatrix);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Error drawing bone weights: {e.Message}");
        }
    }

    private static Mesh GenerateBoneWeightMesh(SkinnedMeshRenderer p_skinnedMesh, int p_boneIndex)
    {
        if (p_skinnedMesh.sharedMesh == null)
            return null;

        Mesh mesh = new Mesh();
        p_skinnedMesh.BakeMesh(mesh);

        Color[] colors = new Color[mesh.vertexCount];

        // Handle both old and new bone weight systems
        if (p_skinnedMesh.sharedMesh.HasVertexAttribute(UnityEngine.Rendering.VertexAttribute.BlendWeight))
        {
            // Use modern bone weight system
            var allBoneWeights = p_skinnedMesh.sharedMesh.GetAllBoneWeights();
            var bonesPerVertex = p_skinnedMesh.sharedMesh.GetBonesPerVertex();

            int weightIndex = 0;
            for (int i = 0; i < mesh.vertexCount; i++)
            {
                colors[i] = GetBoneWeightColorFromNative(allBoneWeights, bonesPerVertex, ref weightIndex, i, p_boneIndex);
            }
        }
        else
        {
            // Fallback to legacy bone weights
            BoneWeight[] boneWeights = p_skinnedMesh.sharedMesh.boneWeights;
            for (int i = 0; i < mesh.vertexCount && i < boneWeights.Length; i++)
            {
                colors[i] = GetBoneWeightColor(boneWeights[i], p_boneIndex);
            }
        }

        mesh.colors = colors;
        return mesh;
    }

    private static Color GetBoneWeightColorFromNative(Unity.Collections.NativeArray<BoneWeight1> allBoneWeights,
        Unity.Collections.NativeArray<byte> bonesPerVertex, ref int weightIndex, int vertexIndex, int targetBoneIndex)
    {
        if (vertexIndex >= bonesPerVertex.Length)
            return Color.clear;

        byte boneCount = bonesPerVertex[vertexIndex];
        float weight = 0f;

        for (int i = 0; i < boneCount; i++)
        {
            if (weightIndex + i < allBoneWeights.Length)
            {
                var boneWeight = allBoneWeights[weightIndex + i];
                if (boneWeight.boneIndex == targetBoneIndex)
                {
                    weight = boneWeight.weight;
                    break;
                }
            }
        }

        weightIndex += boneCount;

        return GetWeightColor(weight);
    }

    private static Color GetBoneWeightColor(BoneWeight p_boneWeight, int p_boneIndex)
    {
        float weight = 0f;

        if (p_boneWeight.boneIndex0 == p_boneIndex) weight = p_boneWeight.weight0;
        else if (p_boneWeight.boneIndex1 == p_boneIndex) weight = p_boneWeight.weight1;
        else if (p_boneWeight.boneIndex2 == p_boneIndex) weight = p_boneWeight.weight2;
        else if (p_boneWeight.boneIndex3 == p_boneIndex) weight = p_boneWeight.weight3;

        return GetWeightColor(weight);
    }

    private static Color GetWeightColor(float weight)
    {
        if (weight <= 0f)
            return Color.clear;

        Color baseColor = Config.boneWeightColor;
        if (baseColor.r == 0 && baseColor.g == 0 && baseColor.b == 0)
            baseColor = Color.red; // Default to red if color is not set

        if (Config.useAlphaForWeightColor)
        {
            return new Color(baseColor.r, baseColor.g, baseColor.b, weight);
        }
        else
        {
            return new Color(baseColor.r * weight, baseColor.g * weight, baseColor.b * weight, 1f);
        }
    }

    private static string[] GetBoneNames(SkinnedMeshRenderer p_skinnedMesh)
    {
        if (p_skinnedMesh == null || p_skinnedMesh.bones == null)
            return new string[0];

        var boneNames = new List<string>();
        var nameCount = new Dictionary<string, int>();

        // First pass: count duplicate names
        for (int i = 0; i < p_skinnedMesh.bones.Length; i++)
        {
            if (p_skinnedMesh.bones[i] != null)
            {
                string baseName = p_skinnedMesh.bones[i].name;
                nameCount[baseName] = nameCount.ContainsKey(baseName) ? nameCount[baseName] + 1 : 1;
            }
        }

        // Second pass: create unique display names
        var usedNames = new Dictionary<string, int>();
        for (int i = 0; i < p_skinnedMesh.bones.Length; i++)
        {
            if (p_skinnedMesh.bones[i] != null)
            {
                string baseName = p_skinnedMesh.bones[i].name;
                string displayName;

                if (nameCount[baseName] > 1)
                {
                    // Add unique identifier for duplicate names
                    int counter = usedNames.ContainsKey(baseName) ? usedNames[baseName] + 1 : 1;
                    usedNames[baseName] = counter;
                    displayName = $"{baseName} [{i:D2}]"; // Use bone index as unique identifier
                }
                else
                {
                    displayName = baseName;
                }

                boneNames.Add(displayName);
            }
        }

        return boneNames.ToArray();
    }

    // Utility methods for future expansion
    public static void SelectBone(Transform bone)
    {
        _selectedBone = bone;
        if (_currentSkinnedMesh != null && _currentSkinnedMesh.bones != null)
        {
            for (int i = 0; i < _currentSkinnedMesh.bones.Length; i++)
            {
                if (_currentSkinnedMesh.bones[i] == bone)
                {
                    Config.boneIndex = i;
                    break;
                }
            }
        }

        // Also update Unity's selection to maintain consistency
        if (bone != null)
        {
            Selection.activeGameObject = bone.gameObject;
        }

        // If we're in View mode and a bone is selected, suggest switching to Move mode
        if (_currentToolMode == ToolMode.View && bone != null)
        {
            Debug.Log($"Bone '{bone.name}' selected. Switch to Move/Rotate mode to manipulate it.");
        }

        SceneView.RepaintAll();
    }

    public static Transform GetSelectedBone()
    {
        return _selectedBone;
    }

    public static ToolMode GetCurrentToolMode()
    {
        return _currentToolMode;
    }

    public static void SetToolMode(ToolMode mode)
    {
        _currentToolMode = mode;
        SceneView.RepaintAll();
    }
}
#endif