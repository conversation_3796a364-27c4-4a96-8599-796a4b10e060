#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace SkinnedMeshTools
{
    public static class AutomaticWeightSystem
    {
        // Store bone influence radius data
        private static Dictionary<Transform, float> _boneRadiusData = new Dictionary<Transform, float>();
        private static readonly float DEFAULT_BONE_RADIUS = 1.0f;
        private static readonly float MIN_BONE_RADIUS = 0.001f; // Very small minimum for precision

        public static float GetBoneRadius(Transform bone)
        {
            if (bone == null) return DEFAULT_BONE_RADIUS;

            if (!_boneRadiusData.ContainsKey(bone))
            {
                _boneRadiusData[bone] = DEFAULT_BONE_RADIUS;
            }

            return _boneRadiusData[bone];
        }

        public static void SetBoneRadius(Transform bone, float radius)
        {
            if (bone == null) return;

            _boneRadiusData[bone] = Mathf.Max(MIN_BONE_RADIUS, radius);
        }

        // Convert linear slider value to exponential radius for better small value control
        public static float SliderToRadius(float sliderValue)
        {
            // Use exponential scaling: radius = 0.01 * (10^(sliderValue * 3))
            // This gives range from 0.01 to 10.0 with better control at small values
            return 0.01f * Mathf.Pow(10f, sliderValue * 3f);
        }

        // Convert radius back to slider value
        public static float RadiusToSlider(float radius)
        {
            // Inverse of SliderToRadius: sliderValue = log10(radius/0.01) / 3
            return Mathf.Log10(Mathf.Max(radius, MIN_BONE_RADIUS) / 0.01f) / 3f;
        }
        
        public static void RecalculateWeights(SkinnedMeshRenderer skinnedMesh)
        {
            if (skinnedMesh == null || skinnedMesh.sharedMesh == null || skinnedMesh.bones == null)
                return;
                
            Undo.RecordObject(skinnedMesh, "Recalculate Automatic Weights");
            
            Mesh mesh = skinnedMesh.sharedMesh;
            Vector3[] vertices = mesh.vertices;
            Transform[] bones = skinnedMesh.bones;
            
            // Calculate weights for each vertex
            List<BoneWeight1> allBoneWeights = new List<BoneWeight1>();
            List<byte> bonesPerVertex = new List<byte>();
            
            for (int vertexIndex = 0; vertexIndex < vertices.Length; vertexIndex++)
            {
                Vector3 worldVertex = skinnedMesh.transform.TransformPoint(vertices[vertexIndex]);
                List<BoneWeight1> vertexWeights = new List<BoneWeight1>();
                
                // Calculate influence from each bone
                for (int boneIndex = 0; boneIndex < bones.Length; boneIndex++)
                {
                    if (bones[boneIndex] == null) continue;
                    
                    float distance = Vector3.Distance(worldVertex, bones[boneIndex].position);
                    float radius = GetBoneRadius(bones[boneIndex]);
                    
                    if (distance <= radius)
                    {
                        // Calculate weight based on distance (closer = higher weight)
                        float weight = 1.0f - (distance / radius);
                        weight = Mathf.Pow(weight, 2); // Square for smoother falloff
                        
                        if (weight > 0.001f) // Only add significant weights
                        {
                            vertexWeights.Add(new BoneWeight1
                            {
                                boneIndex = boneIndex,
                                weight = weight
                            });
                        }
                    }
                }
                
                // Normalize weights
                float totalWeight = vertexWeights.Sum(w => w.weight);
                if (totalWeight > 0)
                {
                    for (int i = 0; i < vertexWeights.Count; i++)
                    {
                        var weight = vertexWeights[i];
                        weight.weight /= totalWeight;
                        vertexWeights[i] = weight;
                    }
                }
                else
                {
                    // If no bone influences this vertex, assign to closest bone
                    int closestBoneIndex = FindClosestBone(worldVertex, bones);
                    if (closestBoneIndex >= 0)
                    {
                        vertexWeights.Add(new BoneWeight1
                        {
                            boneIndex = closestBoneIndex,
                            weight = 1.0f
                        });
                    }
                }
                
                // Limit to 4 bones per vertex (Unity limitation)
                if (vertexWeights.Count > 4)
                {
                    vertexWeights = vertexWeights.OrderByDescending(w => w.weight).Take(4).ToList();
                    
                    // Renormalize after limiting
                    totalWeight = vertexWeights.Sum(w => w.weight);
                    for (int i = 0; i < vertexWeights.Count; i++)
                    {
                        var weight = vertexWeights[i];
                        weight.weight /= totalWeight;
                        vertexWeights[i] = weight;
                    }
                }
                
                bonesPerVertex.Add((byte)vertexWeights.Count);
                allBoneWeights.AddRange(vertexWeights);
            }
            
            // Apply weights to mesh
            var nativeArray = new Unity.Collections.NativeArray<BoneWeight1>(allBoneWeights.ToArray(), Unity.Collections.Allocator.Temp);
            var bonesPerVertexArray = new Unity.Collections.NativeArray<byte>(bonesPerVertex.ToArray(), Unity.Collections.Allocator.Temp);
            
            mesh.SetBoneWeights(bonesPerVertexArray, nativeArray);
            
            nativeArray.Dispose();
            bonesPerVertexArray.Dispose();
            
            EditorUtility.SetDirty(skinnedMesh);
            Debug.Log($"Recalculated automatic weights for {vertices.Length} vertices using {bones.Length} bones");
        }
        
        private static int FindClosestBone(Vector3 worldPosition, Transform[] bones)
        {
            int closestIndex = -1;
            float closestDistance = float.MaxValue;
            
            for (int i = 0; i < bones.Length; i++)
            {
                if (bones[i] == null) continue;
                
                float distance = Vector3.Distance(worldPosition, bones[i].position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestIndex = i;
                }
            }
            
            return closestIndex;
        }
        
        public static void DrawBoneInfluenceGizmos(SkinnedMeshRenderer skinnedMesh)
        {
            if (skinnedMesh == null || skinnedMesh.bones == null) return;

            foreach (Transform bone in skinnedMesh.bones)
            {
                if (bone == null) continue;

                float radius = GetBoneRadius(bone);

                // Ensure minimum visual radius for very small values
                float visualRadius = Mathf.Max(radius, 0.05f); // Minimum visual size

                // Draw influence sphere with actual radius
                Handles.color = new Color(0, 1, 0, 0.1f);
                Handles.DrawSolidDisc(bone.position, Camera.current.transform.forward, radius);

                // Draw wire disc with enhanced visibility for small radii
                Handles.color = new Color(0, 1, 0, radius < 0.1f ? 0.8f : 0.3f); // More opaque for small radii
                Handles.DrawWireDisc(bone.position, Camera.current.transform.forward, radius);

                // Draw additional inner circle for very small radii to show precision
                if (radius < 0.1f)
                {
                    Handles.color = new Color(1, 1, 0, 0.6f); // Yellow inner circle
                    Handles.DrawWireDisc(bone.position, Camera.current.transform.forward, radius * 0.5f);
                }

                // Draw radius label with better formatting for small values
                Handles.color = Color.white;
                string radiusText = radius < 0.01f ? $"{radius:F4}" : $"{radius:F3}";
                Handles.Label(bone.position + Vector3.up * Mathf.Max(radius, 0.2f), $"{bone.name}\nRadius: {radiusText}");
            }
        }
    }
}
#endif
