#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace SkinnedMeshTools
{
    public static class AutomaticWeightSystem
    {
        // Store bone influence radius data
        private static Dictionary<Transform, float> _boneRadiusData = new Dictionary<Transform, float>();
        private static readonly float DEFAULT_BONE_RADIUS = 1.0f;
        
        public static float GetBoneRadius(Transform bone)
        {
            if (bone == null) return DEFAULT_BONE_RADIUS;
            
            if (!_boneRadiusData.ContainsKey(bone))
            {
                _boneRadiusData[bone] = DEFAULT_BONE_RADIUS;
            }
            
            return _boneRadiusData[bone];
        }
        
        public static void SetBoneRadius(Transform bone, float radius)
        {
            if (bone == null) return;
            
            _boneRadiusData[bone] = Mathf.Max(0.01f, radius); // Minimum radius of 0.01
        }
        
        public static void RecalculateWeights(SkinnedMeshRenderer skinnedMesh)
        {
            if (skinnedMesh == null || skinnedMesh.sharedMesh == null || skinnedMesh.bones == null)
                return;
                
            Undo.RecordObject(skinnedMesh, "Recalculate Automatic Weights");
            
            Mesh mesh = skinnedMesh.sharedMesh;
            Vector3[] vertices = mesh.vertices;
            Transform[] bones = skinnedMesh.bones;
            
            // Calculate weights for each vertex
            List<BoneWeight1> allBoneWeights = new List<BoneWeight1>();
            List<byte> bonesPerVertex = new List<byte>();
            
            for (int vertexIndex = 0; vertexIndex < vertices.Length; vertexIndex++)
            {
                Vector3 worldVertex = skinnedMesh.transform.TransformPoint(vertices[vertexIndex]);
                List<BoneWeight1> vertexWeights = new List<BoneWeight1>();
                
                // Calculate influence from each bone
                for (int boneIndex = 0; boneIndex < bones.Length; boneIndex++)
                {
                    if (bones[boneIndex] == null) continue;
                    
                    float distance = Vector3.Distance(worldVertex, bones[boneIndex].position);
                    float radius = GetBoneRadius(bones[boneIndex]);
                    
                    if (distance <= radius)
                    {
                        // Calculate weight based on distance (closer = higher weight)
                        float weight = 1.0f - (distance / radius);
                        weight = Mathf.Pow(weight, 2); // Square for smoother falloff
                        
                        if (weight > 0.001f) // Only add significant weights
                        {
                            vertexWeights.Add(new BoneWeight1
                            {
                                boneIndex = boneIndex,
                                weight = weight
                            });
                        }
                    }
                }
                
                // Normalize weights
                float totalWeight = vertexWeights.Sum(w => w.weight);
                if (totalWeight > 0)
                {
                    for (int i = 0; i < vertexWeights.Count; i++)
                    {
                        var weight = vertexWeights[i];
                        weight.weight /= totalWeight;
                        vertexWeights[i] = weight;
                    }
                }
                else
                {
                    // If no bone influences this vertex, assign to closest bone
                    int closestBoneIndex = FindClosestBone(worldVertex, bones);
                    if (closestBoneIndex >= 0)
                    {
                        vertexWeights.Add(new BoneWeight1
                        {
                            boneIndex = closestBoneIndex,
                            weight = 1.0f
                        });
                    }
                }
                
                // Limit to 4 bones per vertex (Unity limitation)
                if (vertexWeights.Count > 4)
                {
                    vertexWeights = vertexWeights.OrderByDescending(w => w.weight).Take(4).ToList();
                    
                    // Renormalize after limiting
                    totalWeight = vertexWeights.Sum(w => w.weight);
                    for (int i = 0; i < vertexWeights.Count; i++)
                    {
                        var weight = vertexWeights[i];
                        weight.weight /= totalWeight;
                        vertexWeights[i] = weight;
                    }
                }
                
                bonesPerVertex.Add((byte)vertexWeights.Count);
                allBoneWeights.AddRange(vertexWeights);
            }
            
            // Apply weights to mesh
            var nativeArray = new Unity.Collections.NativeArray<BoneWeight1>(allBoneWeights.ToArray(), Unity.Collections.Allocator.Temp);
            var bonesPerVertexArray = new Unity.Collections.NativeArray<byte>(bonesPerVertex.ToArray(), Unity.Collections.Allocator.Temp);
            
            mesh.SetBoneWeights(bonesPerVertexArray, nativeArray);
            
            nativeArray.Dispose();
            bonesPerVertexArray.Dispose();
            
            EditorUtility.SetDirty(skinnedMesh);
            Debug.Log($"Recalculated automatic weights for {vertices.Length} vertices using {bones.Length} bones");
        }
        
        private static int FindClosestBone(Vector3 worldPosition, Transform[] bones)
        {
            int closestIndex = -1;
            float closestDistance = float.MaxValue;
            
            for (int i = 0; i < bones.Length; i++)
            {
                if (bones[i] == null) continue;
                
                float distance = Vector3.Distance(worldPosition, bones[i].position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestIndex = i;
                }
            }
            
            return closestIndex;
        }
        
        public static void DrawBoneInfluenceGizmos(SkinnedMeshRenderer skinnedMesh)
        {
            if (skinnedMesh == null || skinnedMesh.bones == null) return;
            
            foreach (Transform bone in skinnedMesh.bones)
            {
                if (bone == null) continue;
                
                float radius = GetBoneRadius(bone);
                
                // Draw influence sphere
                Handles.color = new Color(0, 1, 0, 0.1f);
                Handles.DrawSolidDisc(bone.position, Camera.current.transform.forward, radius);
                
                Handles.color = new Color(0, 1, 0, 0.3f);
                Handles.DrawWireDisc(bone.position, Camera.current.transform.forward, radius);
                
                // Draw radius label
                Handles.color = Color.white;
                Handles.Label(bone.position + Vector3.up * radius, $"{bone.name}\nRadius: {radius:F2}");
            }
        }
    }
}
#endif
