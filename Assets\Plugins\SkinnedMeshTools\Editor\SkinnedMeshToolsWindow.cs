#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Unity.Collections;
using SkinnedMeshTools;

public class SkinnedMeshToolsWindow : EditorWindow
{
    private Vector2 scrollPosition;
    private SkinnedMeshRenderer currentSkinnedMesh;
    private Transform selectedBone;
    private bool showAdvancedSettings = false;
    private bool showAutomaticWeightSettings = false;
    private bool showAdvancedBrushSettings = false;
    
    // Remove duplicate menu item - now handled by SkinnedMeshToolsEditorMenu.cs
    public static SkinnedMeshToolsWindow ShowWindow()
    {
        var window = GetWindow<SkinnedMeshToolsWindow>("SkinnedMesh Tools");
        window.minSize = new Vector2(300, 400);
        return window;
    }

    private void OnEnable()
    {
        Selection.selectionChanged += OnSelectionChanged;
        SceneView.duringSceneGui += OnSceneGUI;
    }

    private void OnDisable()
    {
        Selection.selectionChanged -= OnSelectionChanged;
        SceneView.duringSceneGui -= OnSceneGUI;
    }

    private void OnSelectionChanged()
    {
        UpdateCurrentSkinnedMesh();
        Repaint();
    }

    private void UpdateCurrentSkinnedMesh()
    {
        GameObject selected = Selection.activeGameObject;
        if (selected != null)
        {
            currentSkinnedMesh = selected.GetComponent<SkinnedMeshRenderer>();
            if (currentSkinnedMesh == null)
                currentSkinnedMesh = selected.GetComponentInChildren<SkinnedMeshRenderer>();
        }
        else
        {
            currentSkinnedMesh = null;
        }
    }

    private void OnSceneGUI(SceneView sceneView)
    {
        if (currentSkinnedMesh != null && SkinnedMeshToolsEditorCore.Config.enabled)
        {
            // This will be handled by the core system
        }
    }

    private void OnGUI()
    {
        var config = SkinnedMeshToolsEditorCore.Config;
        if (config == null)
        {
            EditorGUILayout.HelpBox("SkinnedMeshTools configuration not loaded. Please try reopening the window.", MessageType.Error);
            return;
        }

        // Ensure UI responsiveness by handling events properly
        if (Event.current.type == EventType.Layout || Event.current.type == EventType.Repaint)
        {
            // Force repaint if needed to keep UI responsive
            if (GUI.changed)
            {
                Repaint();
            }
        }

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        DrawHeader();
        DrawMainControls();
        
        if (currentSkinnedMesh != null)
        {
            DrawBoneControls();
            DrawToolModeControls();
            DrawWeightControls();

            if (showAdvancedSettings)
            {
                DrawMeshEditingControls();
                DrawImportExportControls();
                DrawAdvancedSettings();
            }
            else
            {
                EditorGUILayout.Space();
                if (GUILayout.Button("Show Advanced Options"))
                {
                    showAdvancedSettings = true;
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox("Select a GameObject with a SkinnedMeshRenderer to begin manual bone placement.", MessageType.Info);
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("To get started:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("1. Select a mesh object", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("2. Use Tools > SkinnedMeshTools > Convert > Quick Convert", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("3. Return here to manually place bones", EditorStyles.miniLabel);
        }
        
        EditorGUILayout.EndScrollView();
    }

    private void DrawHeader()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        
        var headerStyle = new GUIStyle(EditorStyles.boldLabel);
        headerStyle.fontSize = 16;
        headerStyle.alignment = TextAnchor.MiddleCenter;
        
        EditorGUILayout.LabelField("SkinnedMesh Tools v2.0", headerStyle);
        EditorGUILayout.LabelField("Manual Bone Placement Edition", EditorStyles.miniLabel);
        EditorGUILayout.Space();

        var config = SkinnedMeshToolsEditorCore.Config;
        config.enabled = EditorGUILayout.Toggle("Enable Tools", config.enabled);

        if (!config.enabled)
        {
            EditorGUILayout.HelpBox("Enable tools to start manually placing and adjusting bones.", MessageType.Info);
        }
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawMainControls()
    {
        var config = SkinnedMeshToolsEditorCore.Config;
        
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Visualization", EditorStyles.boldLabel);
        
        config.showBones = EditorGUILayout.Toggle("Show Bones", config.showBones);
        config.showBoneWeights = EditorGUILayout.Toggle("Show Bone Weights", config.showBoneWeights);
        
        if (config.showBones)
        {
            EditorGUI.indentLevel++;
            config.showBoneConnections = EditorGUILayout.Toggle("Show Connections", config.showBoneConnections);
            config.showBoneNames = EditorGUILayout.Toggle("Show Names on Hover", config.showBoneNames);
            config.boneHandleSize = EditorGUILayout.Slider("Handle Size", config.boneHandleSize, 0.5f, 3.0f);
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawBoneControls()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Manual Bone Placement", EditorStyles.boldLabel);

        // Bone Creation Section
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Add New Bone", EditorStyles.miniButton))
        {
            Vector3 centerPosition = currentSkinnedMesh.bounds.center;

            // Determine proper parent for new bone
            Transform parentBone = currentSkinnedMesh.transform; // Default fallback
            if (currentSkinnedMesh.bones.Length > 0 && currentSkinnedMesh.bones[0] != null)
            {
                parentBone = currentSkinnedMesh.bones[0]; // Use root bone as parent
            }

            Transform newBone = BoneManipulationTools.CreateBoneAtPosition(parentBone, centerPosition, "New Bone");
            BoneManipulationTools.AddBoneToSkinnedMesh(currentSkinnedMesh, newBone);
            Selection.activeGameObject = newBone.gameObject;
        }
        if (GUILayout.Button("Add Bone at Origin", EditorStyles.miniButton))
        {
            // Determine proper parent for new bone
            Transform parentBone = currentSkinnedMesh.transform; // Default fallback
            if (currentSkinnedMesh.bones.Length > 0 && currentSkinnedMesh.bones[0] != null)
            {
                parentBone = currentSkinnedMesh.bones[0]; // Use root bone as parent
            }

            Transform newBone = BoneManipulationTools.CreateBoneAtPosition(parentBone, Vector3.zero, "New Bone");
            BoneManipulationTools.AddBoneToSkinnedMesh(currentSkinnedMesh, newBone);
            Selection.activeGameObject = newBone.gameObject;
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.HelpBox("New bones have zero weight by default. Use weight painting to assign influence manually.", MessageType.Info);

        EditorGUILayout.Space();

        if (currentSkinnedMesh.bones != null && currentSkinnedMesh.bones.Length > 0)
        {
            string[] boneNames = currentSkinnedMesh.bones.Where(b => b != null).Select(b => b.name).ToArray();
            var config = SkinnedMeshToolsEditorCore.Config;

            config.boneIndex = Mathf.Clamp(config.boneIndex, 0, boneNames.Length - 1);
            int newBoneIndex = EditorGUILayout.Popup("Selected Bone", config.boneIndex, boneNames);

            if (newBoneIndex != config.boneIndex)
            {
                config.boneIndex = newBoneIndex;
                SkinnedMeshToolsEditorCore.SelectBone(currentSkinnedMesh.bones[newBoneIndex]);
            }

            selectedBone = SkinnedMeshToolsEditorCore.GetSelectedBone();
            if (selectedBone != null)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField($"Selected: {selectedBone.name}", EditorStyles.miniLabel);

                // Precise Position Controls
                EditorGUILayout.LabelField("Precise Positioning", EditorStyles.boldLabel);

                EditorGUI.BeginChangeCheck();
                Vector3 worldPosition = EditorGUILayout.Vector3Field("World Position", selectedBone.position);
                Vector3 localPosition = EditorGUILayout.Vector3Field("Local Position", selectedBone.localPosition);
                Vector3 rotation = EditorGUILayout.Vector3Field("Rotation", selectedBone.eulerAngles);

                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(selectedBone, "Modify Bone Transform");
                    selectedBone.position = worldPosition;
                    selectedBone.localPosition = localPosition;
                    selectedBone.eulerAngles = rotation;
                }

                EditorGUILayout.Space();

                // Quick Position Buttons
                EditorGUILayout.LabelField("Quick Actions", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Center on Mesh", EditorStyles.miniButton))
                {
                    BoneManipulationTools.SetBonePrecisePosition(selectedBone, currentSkinnedMesh.bounds.center);
                }
                if (GUILayout.Button("Move to Origin", EditorStyles.miniButton))
                {
                    BoneManipulationTools.SetBonePrecisePosition(selectedBone, Vector3.zero);
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Duplicate Bone", EditorStyles.miniButton))
                {
                    BoneManipulationTools.DuplicateBone(selectedBone);
                }
                if (GUILayout.Button("Delete Bone", EditorStyles.miniButton))
                {
                    if (EditorUtility.DisplayDialog("Delete Bone", $"Are you sure you want to delete bone '{selectedBone.name}'?", "Delete", "Cancel"))
                    {
                        BoneManipulationTools.DeleteBone(selectedBone, currentSkinnedMesh);
                    }
                }
                EditorGUILayout.EndHorizontal();
            }
        }
        else
        {
            EditorGUILayout.LabelField("No bones found in SkinnedMeshRenderer");
            EditorGUILayout.Space();
            if (GUILayout.Button("Create Initial Bone Structure"))
            {
                // Create a basic bone structure
                Vector3[] positions = new Vector3[]
                {
                    currentSkinnedMesh.bounds.center + Vector3.up * currentSkinnedMesh.bounds.size.y * 0.25f,
                    currentSkinnedMesh.bounds.center,
                    currentSkinnedMesh.bounds.center + Vector3.down * currentSkinnedMesh.bounds.size.y * 0.25f
                };

                List<Transform> newBones = BoneManipulationTools.CreateBonesAtPositions(currentSkinnedMesh.transform, positions);

                // Add bones to the skinned mesh renderer (no automatic weights)
                foreach (Transform bone in newBones)
                {
                    BoneManipulationTools.AddBoneToSkinnedMesh(currentSkinnedMesh, bone);
                }

                EditorUtility.DisplayDialog("Bones Created",
                    "Initial bone structure created. Only the root bone has weight assigned.\n" +
                    "Use weight painting to manually assign influence to other bones.", "OK");
            }
        }

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawWeightControls()
    {
        var config = SkinnedMeshToolsEditorCore.Config;
        
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Weight Visualization", EditorStyles.boldLabel);
        
        if (config.showBoneWeights)
        {
            config.boneWeightColor = EditorGUILayout.ColorField("Weight Color", config.boneWeightColor);
            config.useAlphaForWeightColor = EditorGUILayout.Toggle("Use Alpha for Weight", config.useAlphaForWeightColor);
            
            EditorGUILayout.Space();
            showAutomaticWeightSettings = EditorGUILayout.Foldout(showAutomaticWeightSettings, "Automatic Weight Settings");

            if (showAutomaticWeightSettings)
            {
                EditorGUI.indentLevel++;

                // Auto recalculate toggle
                EditorGUI.BeginChangeCheck();
                config.autoRecalculateWeights = EditorGUILayout.Toggle("Live Weight Updates", config.autoRecalculateWeights);
                if (EditorGUI.EndChangeCheck())
                {
                    EditorUtility.SetDirty(config);
                }

                EditorGUILayout.HelpBox("When enabled, weights automatically update when bones are moved, rotated, or scaled.", MessageType.Info);

                EditorGUILayout.Space();
                config.defaultBoneRadius = EditorGUILayout.Slider("Default Bone Radius", config.defaultBoneRadius, 0.01f, 10.0f);

                EditorGUILayout.Space();
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Recalculate All Weights", EditorStyles.miniButton))
                {
                    if (currentSkinnedMesh != null)
                    {
                        SkinnedMeshTools.AutomaticWeightSystem.RecalculateWeights(currentSkinnedMesh);
                        Repaint();
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space();

                // Advanced Brush Settings
                showAdvancedBrushSettings = EditorGUILayout.Foldout(showAdvancedBrushSettings, "Advanced Brush Settings");
                if (showAdvancedBrushSettings)
                {
                    EditorGUI.indentLevel++;
                    AdvancedBrushSystem.DrawBrushSettings();

                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("Save Preset", EditorStyles.miniButton))
                    {
                        string presetName = EditorUtility.SaveFilePanel("Save Brush Preset", "", "BrushPreset", "preset");
                        if (!string.IsNullOrEmpty(presetName))
                        {
                            AdvancedBrushSystem.SaveBrushPreset(System.IO.Path.GetFileNameWithoutExtension(presetName));
                        }
                    }
                    if (GUILayout.Button("Load Preset", EditorStyles.miniButton))
                    {
                        string presetPath = EditorUtility.OpenFilePanel("Load Brush Preset", "", "preset");
                        if (!string.IsNullOrEmpty(presetPath))
                        {
                            AdvancedBrushSystem.LoadBrushPreset(System.IO.Path.GetFileNameWithoutExtension(presetPath));
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                    EditorGUI.indentLevel--;
                }

                EditorGUILayout.Space();

                // Add UI reset button for troubleshooting
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Reset UI State", EditorStyles.miniButton))
                {
                    ResetUIState();
                }
                if (GUILayout.Button("Force Refresh", EditorStyles.miniButton))
                {
                    GUI.changed = true;
                    Repaint();
                    SceneView.RepaintAll();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Tip: Hold Ctrl+Scroll to adjust brush size", EditorStyles.miniLabel);
                EditorGUILayout.LabelField("If buttons stop responding, use 'Reset UI State'", EditorStyles.miniLabel);

                EditorGUI.indentLevel--;
            }
        }

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawToolModeControls()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Bone Manipulation Mode", EditorStyles.boldLabel);

        var currentMode = SkinnedMeshToolsEditorCore.GetCurrentToolMode();

        EditorGUILayout.LabelField("Primary Modes for Manual Bone Placement:", EditorStyles.miniLabel);
        EditorGUILayout.BeginHorizontal();
        // Use proper button logic instead of toggle buttons to prevent UI freezing
        GUI.backgroundColor = currentMode == SkinnedMeshToolsEditorCore.ToolMode.View ? Color.cyan : Color.white;
        if (GUILayout.Button("View", EditorStyles.miniButton))
        {
            SkinnedMeshToolsEditorCore.SetToolMode(SkinnedMeshToolsEditorCore.ToolMode.View);
            GUI.changed = true;
            Repaint();
        }

        GUI.backgroundColor = currentMode == SkinnedMeshToolsEditorCore.ToolMode.Move ? Color.cyan : Color.white;
        if (GUILayout.Button("Move Bones", EditorStyles.miniButton))
        {
            SkinnedMeshToolsEditorCore.SetToolMode(SkinnedMeshToolsEditorCore.ToolMode.Move);
            GUI.changed = true;
            Repaint();
        }
        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.LabelField("Additional Modes:", EditorStyles.miniLabel);
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = currentMode == SkinnedMeshToolsEditorCore.ToolMode.Rotate ? Color.cyan : Color.white;
        if (GUILayout.Button("Rotate", EditorStyles.miniButton))
        {
            SkinnedMeshToolsEditorCore.SetToolMode(SkinnedMeshToolsEditorCore.ToolMode.Rotate);
            GUI.changed = true;
            Repaint();
        }

        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Tip: Use 'Move Bones' mode with the precise XYZ controls above for best results.", EditorStyles.miniLabel);
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawAdvancedSettings()
    {
        var config = SkinnedMeshToolsEditorCore.Config;
        
        showAdvancedSettings = EditorGUILayout.Foldout(showAdvancedSettings, "Advanced Settings");
        
        if (showAdvancedSettings)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.LabelField("Performance", EditorStyles.boldLabel);
            config.useLODForBones = EditorGUILayout.Toggle("Use LOD for Bones", config.useLODForBones);
            if (config.useLODForBones)
            {
                config.lodDistance = EditorGUILayout.FloatField("LOD Distance", config.lodDistance);
                config.maxVisibleBones = EditorGUILayout.IntField("Max Visible Bones", config.maxVisibleBones);
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("UI", EditorStyles.boldLabel);
            config.compactUI = EditorGUILayout.Toggle("Compact UI", config.compactUI);
            EditorGUILayout.LabelField("Keyboard shortcuts disabled to prevent camera interference", EditorStyles.miniLabel);

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Bone Weight System", EditorStyles.boldLabel);
            if (currentSkinnedMesh != null && currentSkinnedMesh.sharedMesh != null)
            {
                bool hasModernWeights = currentSkinnedMesh.sharedMesh.HasVertexAttribute(UnityEngine.Rendering.VertexAttribute.BlendWeight);
                EditorGUILayout.LabelField("Current System:", hasModernWeights ? "Modern (NativeArray)" : "Legacy (BoneWeight)");

                if (!hasModernWeights)
                {
                    EditorGUILayout.HelpBox("This mesh uses the legacy bone weight system. Consider upgrading to the modern system for better performance and more bone influences per vertex.", MessageType.Info);
                    if (GUILayout.Button("Upgrade to Modern System"))
                    {
                        UpgradeToModernBoneWeights();
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox("This mesh uses the modern bone weight system with support for up to 8 bone influences per vertex.", MessageType.Info);
                }

                if (GUILayout.Button("Normalize All Bone Weights"))
                {
                    if (hasModernWeights)
                    {
                        BoneManipulationTools.NormalizeModernBoneWeights(currentSkinnedMesh);
                    }
                    else
                    {
                        var weights = currentSkinnedMesh.sharedMesh.boneWeights;
                        BoneManipulationTools.NormalizeBoneWeights(weights);

                        Mesh newMesh = Object.Instantiate(currentSkinnedMesh.sharedMesh);
                        newMesh.boneWeights = weights;

                        Undo.RecordObject(currentSkinnedMesh, "Normalize All Bone Weights");
                        currentSkinnedMesh.sharedMesh = newMesh;
                    }
                }

                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Performance Tools", EditorStyles.boldLabel);

                SkinnedMeshToolsPerformanceMonitor.EnableProfiling = EditorGUILayout.Toggle("Enable Performance Monitoring", SkinnedMeshToolsPerformanceMonitor.EnableProfiling);

                if (SkinnedMeshToolsPerformanceMonitor.EnableProfiling)
                {
                    SkinnedMeshToolsPerformanceMonitor.DrawPerformanceStats();
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Analyze Mesh Performance"))
                {
                    SkinnedMeshToolsPerformanceMonitor.OptimizeMeshForEditing(currentSkinnedMesh);
                }
                if (GUILayout.Button("Cleanup Bone Weights"))
                {
                    SkinnedMeshToolsPerformanceMonitor.CleanupUnusedBoneWeights(currentSkinnedMesh);
                }
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndVertical();
        }
    }

    private void UpgradeToModernBoneWeights()
    {
        if (currentSkinnedMesh?.sharedMesh == null)
            return;

        if (currentSkinnedMesh.sharedMesh.HasVertexAttribute(UnityEngine.Rendering.VertexAttribute.BlendWeight))
        {
            EditorUtility.DisplayDialog("Already Modern", "This mesh already uses the modern bone weight system.", "OK");
            return;
        }

        if (!EditorUtility.DisplayDialog("Upgrade Bone Weights",
            "This will convert the mesh to use the modern bone weight system. This operation cannot be undone. Continue?",
            "Yes", "Cancel"))
        {
            return;
        }

        var oldWeights = currentSkinnedMesh.sharedMesh.boneWeights;
        var newBoneWeights = new List<BoneWeight1>();
        var newBonesPerVertex = new List<byte>();

        foreach (var weight in oldWeights)
        {
            var vertexWeights = new List<BoneWeight1>();

            if (weight.weight0 > 0.001f)
                vertexWeights.Add(new BoneWeight1 { boneIndex = weight.boneIndex0, weight = weight.weight0 });
            if (weight.weight1 > 0.001f)
                vertexWeights.Add(new BoneWeight1 { boneIndex = weight.boneIndex1, weight = weight.weight1 });
            if (weight.weight2 > 0.001f)
                vertexWeights.Add(new BoneWeight1 { boneIndex = weight.boneIndex2, weight = weight.weight2 });
            if (weight.weight3 > 0.001f)
                vertexWeights.Add(new BoneWeight1 { boneIndex = weight.boneIndex3, weight = weight.weight3 });

            newBonesPerVertex.Add((byte)vertexWeights.Count);
            newBoneWeights.AddRange(vertexWeights);
        }

        Mesh newMesh = Object.Instantiate(currentSkinnedMesh.sharedMesh);

        // Convert to NativeArrays for Unity 2022.3+ compatibility
        using (var nativeBonesPerVertex = new NativeArray<byte>(newBonesPerVertex.ToArray(), Allocator.Temp))
        using (var nativeBoneWeights = new NativeArray<BoneWeight1>(newBoneWeights.ToArray(), Allocator.Temp))
        {
            newMesh.SetBoneWeights(nativeBonesPerVertex, nativeBoneWeights);
        }

        Undo.RecordObject(currentSkinnedMesh, "Upgrade to Modern Bone Weights");
        currentSkinnedMesh.sharedMesh = newMesh;

        EditorUtility.DisplayDialog("Upgrade Complete", "Mesh has been upgraded to the modern bone weight system.", "OK");
    }

    private void DrawMeshEditingControls()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Mesh Editing", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Clear Selection", EditorStyles.miniButton))
        {
            MeshEditingTools.ClearSelection();
        }
        if (GUILayout.Button("Select All", EditorStyles.miniButton))
        {
            MeshEditingTools.SelectAll(currentSkinnedMesh);
        }
        if (GUILayout.Button("Invert Selection", EditorStyles.miniButton))
        {
            MeshEditingTools.InvertSelection(currentSkinnedMesh);
        }
        EditorGUILayout.EndHorizontal();

        if (MeshEditingTools.SelectedVertices.Count > 0)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField($"Selected Vertices: {MeshEditingTools.SelectedVertices.Count}");

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Scale 0.9x", EditorStyles.miniButton))
            {
                MeshEditingTools.ScaleSelectedVertices(currentSkinnedMesh, 0.9f);
            }
            if (GUILayout.Button("Scale 1.1x", EditorStyles.miniButton))
            {
                MeshEditingTools.ScaleSelectedVertices(currentSkinnedMesh, 1.1f);
            }
            if (GUILayout.Button("Smooth", EditorStyles.miniButton))
            {
                MeshEditingTools.SmoothSelectedVertices(currentSkinnedMesh);
            }
            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawImportExportControls()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Import/Export", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Export Bone Config", EditorStyles.miniButton))
        {
            string path = EditorUtility.SaveFilePanel("Export Bone Configuration", "", currentSkinnedMesh.name + "_bones.json", "json");
            if (!string.IsNullOrEmpty(path))
            {
                ImportExportTools.ExportBoneConfiguration(currentSkinnedMesh, path);
            }
        }
        if (GUILayout.Button("Import Bone Config", EditorStyles.miniButton))
        {
            string path = EditorUtility.OpenFilePanel("Import Bone Configuration", "", "json");
            if (!string.IsNullOrEmpty(path))
            {
                ImportExportTools.ImportBoneConfiguration(currentSkinnedMesh, path);
            }
        }
        EditorGUILayout.EndHorizontal();

        if (SkinnedMeshToolsEditorCore.Config.boneIndex >= 0 && SkinnedMeshToolsEditorCore.Config.boneIndex < currentSkinnedMesh.bones.Length)
        {
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Export Weight Map", EditorStyles.miniButton))
            {
                string path = EditorUtility.SaveFilePanel("Export Weight Map", "",
                    $"{currentSkinnedMesh.name}_bone{SkinnedMeshToolsEditorCore.Config.boneIndex}_weights.txt", "txt");
                if (!string.IsNullOrEmpty(path))
                {
                    ImportExportTools.ExportWeightMap(currentSkinnedMesh, SkinnedMeshToolsEditorCore.Config.boneIndex, path);
                }
            }
            if (GUILayout.Button("Import Weight Map", EditorStyles.miniButton))
            {
                string path = EditorUtility.OpenFilePanel("Import Weight Map", "", "txt");
                if (!string.IsNullOrEmpty(path))
                {
                    ImportExportTools.ImportWeightMap(currentSkinnedMesh, SkinnedMeshToolsEditorCore.Config.boneIndex, path);
                }
            }
            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void ResetUIState()
    {
        Debug.Log("SkinnedMeshTools: Resetting UI state to fix unresponsive buttons");

        // Reset paint mode to a known state
        WeightPaintingTools.CurrentPaintMode = WeightPaintingTools.PaintMode.Add;

        // Reset tool mode to view
        SkinnedMeshToolsEditorCore.SetToolMode(SkinnedMeshToolsEditorCore.ToolMode.View);

        // Reset GUI state
        GUI.changed = true;
        GUI.backgroundColor = Color.white;

        // Force UI refresh
        Repaint();
        SceneView.RepaintAll();

        // Reset any stuck UI flags
        showAutomaticWeightSettings = false;
        showAdvancedBrushSettings = false;

        Debug.Log("SkinnedMeshTools: UI state reset complete");
    }
}
#endif
