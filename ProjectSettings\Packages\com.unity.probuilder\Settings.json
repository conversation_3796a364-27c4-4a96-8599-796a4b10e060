{"m_Dictionary": {"m_DictionaryValues": [{"type": "UnityEngine.ProBuilder.SemVer, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "about.identifier", "value": "{\"m_Value\":{\"m_Major\":5,\"m_Minor\":2,\"m_Patch\":4,\"m_Build\":-1,\"m_Type\":\"\",\"m_Metadata\":\"\",\"m_Date\":\"\"}}"}, {"type": "UnityEngine.ProBuilder.SemVer, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "preferences.version", "value": "{\"m_Value\":{\"m_Major\":5,\"m_Minor\":2,\"m_Patch\":4,\"m_Build\":-1,\"m_Type\":\"\",\"m_Metadata\":\"\",\"m_Date\":\"\"}}"}, {"type": "UnityEngine.ProBuilder.LogLevel, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "log.level", "value": "{\"m_Value\":3}"}, {"type": "UnityEngine.ProBuilder.LogOutput, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "log.output", "value": "{\"m_Value\":1}"}, {"type": "System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "log.path", "value": "{\"m_Value\":\"ProBuilderLog.txt\"}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "lightmapping.autoUnwrapLightmapUV", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "UnityEngine.ProBuilder.ProBuilderEditor-isUtilityWindow", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.backFaceSelectEnabled", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.toolbarIconGUI", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "experimental.enabled", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.showSceneInfo", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "meshImporter.quads", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "meshImporter.smoothing", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.autoRecalculateCollisions", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "FillHole.selectEntirePath", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "CollapseVertices.collapseToFirst", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.showEditorNotifications", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeComponent.ResetSettings", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeComponent.SettingsEnabled", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "mesh.newShapesSnapToGrid", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "mesh.meshColliderIsConvex", "value": "{\"m_Value\":false}"}, {"type": "UnityEngine.ProBuilder.SelectMode, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "editor.selectMode", "value": "{\"m_Value\":8}"}, {"type": "UnityEngine.ProBuilder.SelectMode, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "editor.lastMeshSelectMode", "value": "{\"m_Value\":8}"}, {"type": "UnityEngine.ProBuilder.SelectionModifierBehavior, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "editor.rectSelectModifier", "value": "{\"m_Value\":2}"}, {"type": "UnityEngine.ProBuilder.RectSelectMode, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "editor.dragSelectRectMode", "value": "{\"m_Value\":0}"}, {"type": "UnityEngine.ProBuilder.ExtrudeMethod, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "editor.extrude<PERSON><PERSON><PERSON>", "value": "{\"m_Value\":2}"}, {"type": "System.Single, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "meshImporter.smoothingAngle", "value": "{\"m_Value\":1.0}"}, {"type": "System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeBuilder.ActiveShapeIndex", "value": "{\"m_Value\":6}"}, {"type": "System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeBuilder.LastPivotLocation", "value": "{\"m_Value\":1}"}, {"type": "UnityEngine.ProBuilder.PivotLocation, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.newShapePivotLocation", "value": "{\"m_Value\":1}"}, {"type": "UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastPivotPosition", "value": "{\"m_Value\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}}"}, {"type": "UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastSize", "value": "{\"m_Value\":{\"x\":-0.25,\"y\":0.25,\"z\":0.25}}"}, {"type": "UnityEngine.Quaternion, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastRotation", "value": "{\"m_Value\":{\"x\":0.0,\"y\":0.0,\"z\":0.0,\"w\":1.0}}"}, {"type": "UnityEngine.Material, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.userMaterial", "value": "{\"m_Value\":{\"instanceID\":0}}"}, {"type": "UnityEngine.Rendering.ShadowCastingMode, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.shadowCastingMode", "value": "{\"m_Value\":1}"}, {"type": "UnityEditor.StaticEditorFlags, UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.defaultStaticEditorFlags", "value": "{\"m_Value\":0}"}, {"type": "UnityEngine.ProBuilder.ColliderType, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.newShapeColliderType", "value": "{\"m_Value\":2}"}, {"type": "UnityEngine.ProBuilder.UnwrapParameters, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "lightmapping.defaultLightmapUnwrapParameters", "value": "{\"m_Value\":{\"m_HardAngle\":88.0,\"m_PackMargin\":20.0,\"m_AngleError\":8.0,\"m_AreaError\":15.0}}"}, {"type": "UnityEngine.ProBuilder.Shapes.Shape, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.Cube", "value": "{}"}]}}