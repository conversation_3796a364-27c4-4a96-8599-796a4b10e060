/*
 *	Created by:  <PERSON> @sHTiF Stefcek
 */

#if UNITY_EDITOR
using System;
using UnityEngine;

[Serializable]
public class SkinnedMeshToolsEditorConfig : ScriptableObject
{
    [Header("General Settings")]
    public bool enabled = true;

    [Header("Bone Visualization")]
    public bool showBones = true;
    public bool showBoneWeights = false;
    public int boneIndex = 0;

    [Header("Weight Visualization")]
    public bool useAlphaForWeightColor = true;
    public Color boneWeightColor = Color.red;

    [Header("Tool Settings")]
    public bool enableEditing = false;
    public float boneHandleSize = 1.0f;
    public bool showBoneNames = true;
    public bool showBoneConnections = true;

    [Header("Performance")]
    public bool useLODForBones = true;
    public float lodDistance = 50f;
    public int maxVisibleBones = 100;

    [Header("Automatic Weights")]
    public bool autoRecalculateWeights = false; // Default to disabled
    public float defaultBoneRadius = 1.0f;

    [Header("Legacy Weight Painting (Deprecated)")]
    public float brushSize = 1.0f;
    public float brushStrength = 0.5f;
    public bool normalizeWeights = true;

    [Header("UI")]
    public bool compactUI = false;
}
#endif