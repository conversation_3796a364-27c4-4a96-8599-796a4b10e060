/*
 *	Created by:  <PERSON> @sHTiF <PERSON>efcek
 *  Enhanced by: AI Assistant
 */

using UnityEditor;

public class SkinnedMeshToolsEditorMenu
{
    [MenuItem("Tools/SkinnedMeshTools/Open Window")]
    static void OpenSkinnedMeshToolsWindow()
    {
        SkinnedMeshToolsWindow.ShowWindow();
    }

    [MenuItem("Tools/SkinnedMeshTools/Enable")]
    static void EnableSkinnedMeshTool()
    {
        if (SkinnedMeshToolsEditorCore.Config != null)
        {
            SkinnedMeshToolsEditorCore.Config.enabled = true;
            SceneView.RepaintAll();
        }
    }

    [MenuItem("Tools/SkinnedMeshTools/Enable", true)]
    static bool ValidateEnableSkinnedMeshTool()
    {
        try
        {
            return SkinnedMeshToolsEditorCore.Config != null && !SkinnedMeshToolsEditorCore.Config.enabled;
        }
        catch
        {
            return true; // Default to enabled if there's an issue
        }
    }

    [MenuItem("Tools/SkinnedMeshTools/Disable")]
    static void DisableSkinnedMeshTool()
    {
        if (SkinnedMeshToolsEditorCore.Config != null)
        {
            SkinnedMeshToolsEditorCore.Config.enabled = false;
            SceneView.RepaintAll();
        }
    }

    [MenuItem("Tools/SkinnedMeshTools/Disable", true)]
    static bool ValidateDisableSkinnedMeshTool()
    {
        try
        {
            return SkinnedMeshToolsEditorCore.Config != null && SkinnedMeshToolsEditorCore.Config.enabled;
        }
        catch
        {
            return false; // Default to disabled if there's an issue
        }
    }

    [MenuItem("Tools/SkinnedMeshTools/Reset Configuration")]
    static void ResetConfiguration()
    {
        if (EditorUtility.DisplayDialog("Reset Configuration",
            "Are you sure you want to reset all SkinnedMeshTools settings to default?",
            "Yes", "No"))
        {
            var config = SkinnedMeshToolsEditorCore.Config;
            config.enabled = true;
            config.showBones = true;
            config.boneIndex = 0;
            config.enableEditing = false;
            config.boneHandleSize = 1.0f;
            config.showBoneNames = true;
            config.showBoneConnections = true;
            config.useLODForBones = true;
            config.lodDistance = 50f;
            config.maxVisibleBones = 100;
            config.brushSize = 1.0f;
            config.brushStrength = 0.5f;
            config.normalizeWeights = true;
            config.compactUI = false;

            EditorUtility.SetDirty(config);
            SceneView.RepaintAll();
        }
    }
}