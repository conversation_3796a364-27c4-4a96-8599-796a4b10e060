#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using Unity.Collections;
using UnityEditor;
using UnityEngine;

[CreateAssetMenu(fileName = "ConversionSettings", menuName = "SkinnedMeshTools/Conversion Settings")]
public class ConversionSettings : ScriptableObject
{
    [Header("Basic Settings")]
    public bool generateRootBone = true;
    public int initialBoneCount = 3;

    [Header("Mesh Processing")]
    public bool preserveBlendShapes = true;
    public bool normalizeWeights = true;
    public int maxInfluencesPerVertex = 4;

    // Legacy compatibility properties for manual bone placement workflow
    [Header("Legacy Compatibility (Manual Bone Placement)")]
    public int maxBones = 32; // Maximum bones for manual placement
    public bool optimizeMesh = false; // Disabled for manual workflow
    public BoneGenerationMode boneGenerationMode = BoneGenerationMode.Manual; // Always manual
    public CharacterType characterType = CharacterType.Custom; // Always custom for manual placement
}

// Simplified enums for manual bone placement workflow
public enum BoneDistributionMode
{
    Linear,     // Distribute bones linearly along mesh bounds
    Grid,       // Place bones in a grid pattern
    Manual      // Manual placement only
}

// Legacy compatibility enums for manual bone placement workflow
public enum BoneGenerationMode
{
    Manual,     // Manual bone placement (default for our workflow)
    Automatic,  // Legacy - not used in manual workflow
    Template    // Legacy - not used in manual workflow
}

public enum CharacterType
{
    Custom,     // Default for manual placement
    Humanoid,   // Legacy compatibility
    Quadruped,  // Legacy compatibility
    Bird,       // Legacy compatibility
    Fish        // Legacy compatibility
}

public static class MeshToSkinnedMeshConverter
{
    private static ConversionSettings currentSettings;
    
    [MenuItem("Tools/SkinnedMeshTools/Convert Mesh to SkinnedMesh")]
    public static void ConvertMeshToSkinnedMeshMenu()
    {
        GameObject selected = Selection.activeGameObject;
        if (selected == null)
        {
            EditorUtility.DisplayDialog("Error", "Please select a GameObject with a MeshRenderer or MeshFilter.", "OK");
            return;
        }

        MeshRenderer meshRenderer = selected.GetComponent<MeshRenderer>();
        MeshFilter meshFilter = selected.GetComponent<MeshFilter>();

        if (meshRenderer == null || meshFilter == null || meshFilter.sharedMesh == null)
        {
            EditorUtility.DisplayDialog("Error", "Selected GameObject must have both MeshRenderer and MeshFilter components with a valid mesh.", "OK");
            return;
        }

        // Show conversion settings window
        ConversionSettingsWindow.ShowWindow(selected);
    }


    
    public static GameObject ConvertMeshToSkinnedMesh(GameObject sourceObject, ConversionSettings settings)
    {
        currentSettings = settings;

        MeshFilter meshFilter = sourceObject.GetComponent<MeshFilter>();
        MeshRenderer meshRenderer = sourceObject.GetComponent<MeshRenderer>();

        if (meshFilter?.sharedMesh == null || meshRenderer == null)
        {
            Debug.LogError("Source object must have valid MeshFilter and MeshRenderer components.");
            return null;
        }

        Undo.IncrementCurrentGroup();
        int undoGroup = Undo.GetCurrentGroup();
        Undo.SetCurrentGroupName("Convert Mesh to SkinnedMesh");

        try
        {
            // Create new GameObject for the skinned mesh
            GameObject skinnedObject = new GameObject(sourceObject.name + "_Skinned");
            Undo.RegisterCreatedObjectUndo(skinnedObject, "Create Skinned Object");

            // Copy transform
            skinnedObject.transform.position = sourceObject.transform.position;
            skinnedObject.transform.rotation = sourceObject.transform.rotation;
            skinnedObject.transform.localScale = sourceObject.transform.localScale;
            skinnedObject.transform.SetParent(sourceObject.transform.parent);

            // Add SkinnedMeshRenderer
            SkinnedMeshRenderer skinnedRenderer = skinnedObject.AddComponent<SkinnedMeshRenderer>();

            // Copy materials properly with validation
            Material[] materials = new Material[meshRenderer.materials.Length];
            Debug.Log($"Copying {meshRenderer.materials.Length} materials from source mesh");

            for (int i = 0; i < meshRenderer.materials.Length; i++)
            {
                materials[i] = meshRenderer.materials[i];
                if (materials[i] != null)
                {
                    Debug.Log($"  Material {i}: {materials[i].name} (Shader: {materials[i].shader.name})");
                }
                else
                {
                    Debug.LogWarning($"  Material {i}: NULL - this may cause rendering issues");
                }
            }

            // Assign materials to skinned renderer
            skinnedRenderer.materials = materials;

            // Validate material assignment
            if (skinnedRenderer.materials.Length != materials.Length)
            {
                Debug.LogError("Material assignment failed - length mismatch!");
            }

            // Copy other renderer properties
            skinnedRenderer.shadowCastingMode = meshRenderer.shadowCastingMode;
            skinnedRenderer.receiveShadows = meshRenderer.receiveShadows;
            skinnedRenderer.lightProbeUsage = meshRenderer.lightProbeUsage;
            skinnedRenderer.reflectionProbeUsage = meshRenderer.reflectionProbeUsage;

            // Process the mesh
            Mesh processedMesh = ProcessMeshForSkinning(meshFilter.sharedMesh);

            // Generate bone structure
            Transform[] bones = GenerateBoneStructure(skinnedObject, processedMesh);

            // Calculate bind poses BEFORE assigning to renderer
            CalculateBindPoses(processedMesh, bones, skinnedObject.transform);

            // Generate bone weights
            GenerateBoneWeights(processedMesh, bones, skinnedObject.transform);

            // Setup the SkinnedMeshRenderer AFTER mesh processing is complete
            skinnedRenderer.sharedMesh = processedMesh;
            skinnedRenderer.bones = bones;

            // CRITICAL: Set root bone properly for correct hierarchy
            if (bones.Length > 0 && bones[0] != null)
            {
                skinnedRenderer.rootBone = bones[0]; // First bone is always the root
                Debug.Log($"Set root bone to: {bones[0].name}");
            }
            else
            {
                Debug.LogWarning("No valid root bone found - this may cause bone hierarchy issues");
                skinnedRenderer.rootBone = skinnedObject.transform;
            }

            // Final validation
            Debug.Log("=== Conversion Validation ===");
            Debug.Log($"Skinned mesh bounds: {skinnedRenderer.bounds}");
            Debug.Log($"Shared mesh bounds: {processedMesh.bounds}");
            Debug.Log($"Materials assigned: {skinnedRenderer.materials.Length}");
            Debug.Log($"Bones created: {bones.Length}");
            Debug.Log($"Bind poses: {processedMesh.bindposes?.Length ?? 0}");

            // Validate no duplicate meshes by checking if bounds are reasonable
            if (skinnedRenderer.bounds.size.magnitude > 1000f)
            {
                Debug.LogError($"WARNING: Skinned mesh has unusually large bounds ({skinnedRenderer.bounds.size}). This may indicate a scaling issue.");
            }

            // Ensure materials are still assigned after mesh setup
            for (int i = 0; i < skinnedRenderer.materials.Length; i++)
            {
                if (skinnedRenderer.materials[i] == null)
                {
                    Debug.LogError($"Material {i} became null after mesh setup!");
                }
            }

            // Hide the original object to prevent material conflicts
            Undo.RecordObject(sourceObject, "Hide Original Object");
            sourceObject.SetActive(false);

            Debug.Log($"Successfully converted {sourceObject.name} to SkinnedMesh with {bones.Length} bones for manual placement.");

            return skinnedObject;
        }
        catch (Exception e)
        {
            Debug.LogError($"Error converting mesh to skinned mesh: {e.Message}");
            Undo.RevertAllDownToGroup(undoGroup);
            return null;
        }
        finally
        {
            Undo.CollapseUndoOperations(undoGroup);
        }
    }
    
    private static Mesh ProcessMeshForSkinning(Mesh originalMesh)
    {
        // Create a clean copy of the mesh
        Mesh processedMesh = UnityEngine.Object.Instantiate(originalMesh);
        processedMesh.name = originalMesh.name + "_Skinned";

        // Debug mesh information
        Debug.Log($"Processing mesh: {originalMesh.name}");
        Debug.Log($"  Vertex count: {originalMesh.vertexCount}");
        Debug.Log($"  Bounds: {originalMesh.bounds}");
        Debug.Log($"  Is readable: {originalMesh.isReadable}");

        // Ensure mesh is readable
        if (!originalMesh.isReadable)
        {
            Debug.LogWarning("Original mesh is not readable. Some features may be limited.");
        }

        // Preserve blend shapes if requested
        if (currentSettings.preserveBlendShapes && originalMesh.blendShapeCount > 0)
        {
            Debug.Log($"Preserving {originalMesh.blendShapeCount} blend shapes.");
        }

        // Ensure mesh bounds are reasonable
        if (processedMesh.bounds.size == Vector3.zero)
        {
            Debug.LogWarning("Mesh has zero bounds - recalculating bounds");
            processedMesh.RecalculateBounds();
        }

        // Mesh optimization disabled for manual bone placement workflow
        // Manual placement requires unoptimized mesh for better bone manipulation

        return processedMesh;
    }
    
    private static Transform[] GenerateBoneStructure(GameObject rootObject, Mesh mesh)
    {
        // Simplified bone generation for manual placement workflow
        return GenerateBasicBoneStructure(rootObject, mesh);
    }
    
    private static Transform[] GenerateBasicBoneStructure(GameObject rootObject, Mesh mesh)
    {
        List<Transform> bones = new List<Transform>();

        // Create root bone - this is essential for proper skinning
        GameObject rootBone = new GameObject("Root");
        rootBone.transform.SetParent(rootObject.transform);
        rootBone.transform.localPosition = Vector3.zero;
        rootBone.transform.localRotation = Quaternion.identity;
        rootBone.transform.localScale = Vector3.one;
        bones.Add(rootBone.transform);

        // Create a minimal set of bones for manual placement
        Bounds meshBounds = mesh.bounds;
        Vector3 localCenter = meshBounds.center;
        Vector3 size = meshBounds.size;

        // Transform mesh bounds center to world space
        Vector3 worldCenter = rootObject.transform.TransformPoint(localCenter);

        Debug.Log($"Mesh local bounds center: {localCenter}");
        Debug.Log($"Mesh world bounds center: {worldCenter}");
        Debug.Log($"Root object position: {rootObject.transform.position}");

        // Create additional bones as children of root bone (proper hierarchy)
        for (int i = 1; i < currentSettings.initialBoneCount; i++)
        {
            GameObject bone = new GameObject($"Bone_{i:00}");
            bone.transform.SetParent(rootBone.transform); // Parent to root bone, not mesh object

            // Place additional bones in a simple linear distribution along Y axis relative to mesh center
            float t = currentSettings.initialBoneCount > 1 ? (float)(i-1) / (currentSettings.initialBoneCount - 2) : 0.5f;

            // Calculate local position relative to mesh center, then transform to world space
            Vector3 localOffset = Vector3.up * (t - 0.5f) * size.y;
            Vector3 localPosition = localCenter + localOffset;
            Vector3 worldPosition = rootObject.transform.TransformPoint(localPosition);

            bone.transform.position = worldPosition;
            bone.transform.localRotation = Quaternion.identity;
            bone.transform.localScale = Vector3.one;

            Debug.Log($"Bone {i}: local pos {localPosition}, world pos {worldPosition}");
            bones.Add(bone.transform);
        }

        Debug.Log($"Generated {bones.Count} basic bones for manual placement in {rootObject.name}");
        return bones.ToArray();
    }

    
    private static void GenerateBoneWeights(Mesh mesh, Transform[] bones, Transform meshTransform)
    {
        if (bones.Length == 0)
        {
            Debug.LogWarning("No bones available for weight generation.");
            return;
        }
        
        Vector3[] vertices = mesh.vertices;
        List<BoneWeight1> boneWeights = new List<BoneWeight1>();
        List<byte> bonesPerVertex = new List<byte>();
        
        for (int vertexIndex = 0; vertexIndex < vertices.Length; vertexIndex++)
        {
            List<(int boneIndex, float weight)> vertexWeights = new List<(int, float)>();

            // MANUAL BONE PLACEMENT: Only assign weight to root bone (index 0) by default
            // Users must manually paint weights for additional bones
            if (bones.Length > 0)
            {
                // Assign all weight to the root bone (first bone)
                vertexWeights.Add((0, 1.0f));
            }

            // Note: Additional bones (index 1+) get zero weight by default
            // This allows users to manually control weight distribution through painting

            // Add to bone weights (simplified - no sorting or normalization needed)
            bonesPerVertex.Add((byte)vertexWeights.Count);
            foreach (var weight in vertexWeights)
            {
                boneWeights.Add(new BoneWeight1
                {
                    boneIndex = weight.boneIndex,
                    weight = weight.weight
                });
            }
        }
        
        // Apply weights to mesh using NativeArrays
        using (var nativeBonesPerVertex = new NativeArray<byte>(bonesPerVertex.ToArray(), Allocator.Temp))
        using (var nativeBoneWeights = new NativeArray<BoneWeight1>(boneWeights.ToArray(), Allocator.Temp))
        {
            mesh.SetBoneWeights(nativeBonesPerVertex, nativeBoneWeights);
        }
    }
    
    private static float CalculateWeight(float distance)
    {
        // Simple distance-based weight calculation
        float falloffRadius = 2.0f; // Fixed falloff radius for simplicity
        return Mathf.Max(0, 1.0f - distance / falloffRadius);
    }
    
    private static void CalculateBindPoses(Mesh mesh, Transform[] bones, Transform meshTransform)
    {
        Matrix4x4[] bindPoses = new Matrix4x4[bones.Length];

        for (int i = 0; i < bones.Length; i++)
        {
            if (bones[i] != null)
            {
                // Calculate bind pose: transform from mesh space to bone space
                bindPoses[i] = bones[i].worldToLocalMatrix * meshTransform.localToWorldMatrix;
            }
            else
            {
                // Fallback for null bones
                bindPoses[i] = Matrix4x4.identity;
                Debug.LogWarning($"Bone at index {i} is null, using identity matrix for bind pose.");
            }
        }

        // Set bind poses directly on the mesh (no duplication)
        mesh.bindposes = bindPoses;
    }

}
#endif
