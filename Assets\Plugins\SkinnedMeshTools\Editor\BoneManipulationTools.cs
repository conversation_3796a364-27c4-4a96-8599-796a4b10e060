#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Unity.Collections;

public static class BoneManipulationTools
{
    public static void CreateBone(Transform parent, string boneName = "New Bone")
    {
        GameObject newBone = new GameObject(boneName);
        newBone.transform.SetParent(parent);
        newBone.transform.localPosition = Vector3.zero;
        newBone.transform.localRotation = Quaternion.identity;
        newBone.transform.localScale = Vector3.one;

        Undo.RegisterCreatedObjectUndo(newBone, "Create Bone");
        Selection.activeGameObject = newBone;
    }

    public static Transform CreateBoneAtPosition(Transform parent, Vector3 worldPosition, string boneName = "New Bone")
    {
        GameObject newBone = new GameObject(boneName);
        newBone.transform.SetParent(parent);
        newBone.transform.position = worldPosition;
        newBone.transform.localRotation = Quaternion.identity;
        newBone.transform.localScale = Vector3.one;

        Undo.RegisterCreatedObjectUndo(newBone, "Create Bone at Position");
        Selection.activeGameObject = newBone;

        return newBone.transform;
    }

    public static void SetBonePrecisePosition(Transform bone, Vector3 worldPosition)
    {
        if (bone == null) return;

        Undo.RecordObject(bone, "Set Bone Precise Position");
        bone.position = worldPosition;
    }

    public static void SetBonePreciseLocalPosition(Transform bone, Vector3 localPosition)
    {
        if (bone == null) return;

        Undo.RecordObject(bone, "Set Bone Precise Local Position");
        bone.localPosition = localPosition;
    }

    public static List<Transform> CreateBonesAtPositions(Transform parent, Vector3[] worldPositions, string baseNamePrefix = "Bone")
    {
        List<Transform> createdBones = new List<Transform>();

        Undo.IncrementCurrentGroup();
        int undoGroup = Undo.GetCurrentGroup();
        Undo.SetCurrentGroupName("Create Multiple Bones");

        for (int i = 0; i < worldPositions.Length; i++)
        {
            string boneName = $"{baseNamePrefix}_{i:00}";
            GameObject newBone = new GameObject(boneName);
            newBone.transform.SetParent(parent);
            newBone.transform.position = worldPositions[i];
            newBone.transform.localRotation = Quaternion.identity;
            newBone.transform.localScale = Vector3.one;

            Undo.RegisterCreatedObjectUndo(newBone, "Create Bone");
            createdBones.Add(newBone.transform);
        }

        Undo.CollapseUndoOperations(undoGroup);

        if (createdBones.Count > 0)
        {
            Selection.activeGameObject = createdBones[0].gameObject;
        }

        return createdBones;
    }

    public static void DeleteBone(Transform bone, SkinnedMeshRenderer skinnedMesh)
    {
        if (bone == null || skinnedMesh == null)
            return;
            
        // Find bone index
        int boneIndex = -1;
        for (int i = 0; i < skinnedMesh.bones.Length; i++)
        {
            if (skinnedMesh.bones[i] == bone)
            {
                boneIndex = i;
                break;
            }
        }
        
        if (boneIndex == -1)
        {
            // Bone not part of skinned mesh, just delete it
            Undo.DestroyObjectImmediate(bone.gameObject);
            return;
        }
        
        // Remove bone from skinned mesh
        RemoveBoneFromSkinnedMesh(skinnedMesh, boneIndex);
        Undo.DestroyObjectImmediate(bone.gameObject);
    }
    
    public static void DuplicateBone(Transform bone)
    {
        if (bone == null)
            return;
            
        GameObject duplicate = Object.Instantiate(bone.gameObject, bone.parent);
        duplicate.name = bone.name + "_Copy";
        
        Undo.RegisterCreatedObjectUndo(duplicate, "Duplicate Bone");
        Selection.activeGameObject = duplicate;
    }
    
    public static void AddBoneToSkinnedMesh(SkinnedMeshRenderer skinnedMesh, Transform newBone)
    {
        if (skinnedMesh == null || newBone == null)
            return;

        Undo.RecordObject(skinnedMesh, "Add Bone to SkinnedMesh");

        // Ensure proper bone hierarchy - new bones should be children of root bone, not mesh
        if (skinnedMesh.bones.Length > 0 && skinnedMesh.bones[0] != null)
        {
            Transform rootBone = skinnedMesh.bones[0];
            if (newBone.parent == skinnedMesh.transform)
            {
                // Move bone from mesh to root bone hierarchy
                newBone.SetParent(rootBone);
                Debug.Log($"Moved bone '{newBone.name}' to be child of root bone '{rootBone.name}'");
            }
        }

        // Add bone to bones array
        var bonesList = skinnedMesh.bones.ToList();
        bonesList.Add(newBone);
        skinnedMesh.bones = bonesList.ToArray();

        // Calculate proper bind pose for the new bone
        Matrix4x4 bindPose;
        if (skinnedMesh.rootBone != null)
        {
            // Use root bone as reference for proper bind pose calculation
            bindPose = newBone.worldToLocalMatrix * skinnedMesh.rootBone.localToWorldMatrix;
        }
        else
        {
            // Fallback to mesh transform
            bindPose = newBone.worldToLocalMatrix * skinnedMesh.transform.localToWorldMatrix;
        }

        // Add bindpose
        var bindPosesList = new List<Matrix4x4>();
        skinnedMesh.sharedMesh.GetBindposes(bindPosesList);
        bindPosesList.Add(bindPose);

        // Create new mesh with updated bindposes but NO automatic weights
        Mesh newMesh = Object.Instantiate(skinnedMesh.sharedMesh);
        newMesh.bindposes = bindPosesList.ToArray();

        // IMPORTANT: Do NOT assign automatic weights to the new bone
        // Users must manually paint weights for new bones
        // This preserves existing weights and leaves the new bone with zero influence

        skinnedMesh.sharedMesh = newMesh;

        Debug.Log($"Added bone '{newBone.name}' to SkinnedMesh. Use 'Recalculate' button to update weights when ready.");
    }
    
    private static void RemoveBoneFromSkinnedMesh(SkinnedMeshRenderer skinnedMesh, int boneIndex)
    {
        Undo.RecordObject(skinnedMesh, "Remove Bone from SkinnedMesh");
        
        // Remove bone from bones array
        var bonesList = skinnedMesh.bones.ToList();
        bonesList.RemoveAt(boneIndex);
        skinnedMesh.bones = bonesList.ToArray();
        
        // Remove bindpose
        var bindPosesList = new List<Matrix4x4>();
        skinnedMesh.sharedMesh.GetBindposes(bindPosesList);
        bindPosesList.RemoveAt(boneIndex);
        
        // Update bone weights to remove references to deleted bone
        var boneWeights = skinnedMesh.sharedMesh.boneWeights;
        for (int i = 0; i < boneWeights.Length; i++)
        {
            boneWeights[i] = RemoveBoneFromWeight(boneWeights[i], boneIndex);
        }
        
        // Create new mesh with updated data
        Mesh newMesh = Object.Instantiate(skinnedMesh.sharedMesh);
        newMesh.bindposes = bindPosesList.ToArray();
        newMesh.boneWeights = boneWeights;
        skinnedMesh.sharedMesh = newMesh;
    }
    
    private static BoneWeight RemoveBoneFromWeight(BoneWeight weight, int removedBoneIndex)
    {
        BoneWeight newWeight = weight;
        
        // Remove weight for the deleted bone and shift indices
        if (weight.boneIndex0 == removedBoneIndex)
        {
            newWeight.boneIndex0 = weight.boneIndex1;
            newWeight.weight0 = weight.weight1;
            newWeight.boneIndex1 = weight.boneIndex2;
            newWeight.weight1 = weight.weight2;
            newWeight.boneIndex2 = weight.boneIndex3;
            newWeight.weight2 = weight.weight3;
            newWeight.boneIndex3 = 0;
            newWeight.weight3 = 0;
        }
        else if (weight.boneIndex1 == removedBoneIndex)
        {
            newWeight.boneIndex1 = weight.boneIndex2;
            newWeight.weight1 = weight.weight2;
            newWeight.boneIndex2 = weight.boneIndex3;
            newWeight.weight2 = weight.weight3;
            newWeight.boneIndex3 = 0;
            newWeight.weight3 = 0;
        }
        else if (weight.boneIndex2 == removedBoneIndex)
        {
            newWeight.boneIndex2 = weight.boneIndex3;
            newWeight.weight2 = weight.weight3;
            newWeight.boneIndex3 = 0;
            newWeight.weight3 = 0;
        }
        else if (weight.boneIndex3 == removedBoneIndex)
        {
            newWeight.boneIndex3 = 0;
            newWeight.weight3 = 0;
        }
        
        // Shift bone indices that are higher than the removed index
        if (newWeight.boneIndex0 > removedBoneIndex) newWeight.boneIndex0--;
        if (newWeight.boneIndex1 > removedBoneIndex) newWeight.boneIndex1--;
        if (newWeight.boneIndex2 > removedBoneIndex) newWeight.boneIndex2--;
        if (newWeight.boneIndex3 > removedBoneIndex) newWeight.boneIndex3--;
        
        return newWeight;
    }
    
    public static void NormalizeBoneWeights(BoneWeight[] weights)
    {
        for (int i = 0; i < weights.Length; i++)
        {
            float totalWeight = weights[i].weight0 + weights[i].weight1 + weights[i].weight2 + weights[i].weight3;
            if (totalWeight > 0)
            {
                weights[i].weight0 /= totalWeight;
                weights[i].weight1 /= totalWeight;
                weights[i].weight2 /= totalWeight;
                weights[i].weight3 /= totalWeight;
            }
        }
    }

    public static void NormalizeModernBoneWeights(SkinnedMeshRenderer skinnedMesh)
    {
        if (skinnedMesh?.sharedMesh == null)
            return;

        if (!skinnedMesh.sharedMesh.HasVertexAttribute(UnityEngine.Rendering.VertexAttribute.BlendWeight))
            return;

        var allBoneWeights = skinnedMesh.sharedMesh.GetAllBoneWeights();
        var bonesPerVertex = skinnedMesh.sharedMesh.GetBonesPerVertex();

        var newBoneWeights = new List<BoneWeight1>();
        var newBonesPerVertex = new List<byte>();

        int weightIndex = 0;
        for (int vertexIndex = 0; vertexIndex < bonesPerVertex.Length; vertexIndex++)
        {
            byte numBones = bonesPerVertex[vertexIndex];
            var vertexWeights = new List<BoneWeight1>();

            // Get weights for this vertex
            float totalWeight = 0;
            for (int i = 0; i < numBones; i++)
            {
                var weight = allBoneWeights[weightIndex + i];
                vertexWeights.Add(weight);
                totalWeight += weight.weight;
            }

            // Normalize if needed
            if (totalWeight > 0.001f && Mathf.Abs(totalWeight - 1.0f) > 0.001f)
            {
                for (int i = 0; i < vertexWeights.Count; i++)
                {
                    var w = vertexWeights[i];
                    w.weight /= totalWeight;
                    vertexWeights[i] = w;
                }
            }

            newBonesPerVertex.Add((byte)vertexWeights.Count);
            newBoneWeights.AddRange(vertexWeights);

            weightIndex += numBones;
        }

        // Create new mesh with normalized weights
        Mesh newMesh = Object.Instantiate(skinnedMesh.sharedMesh);

        // Convert to NativeArrays for Unity 2022.3+ compatibility
        using (var nativeBonesPerVertex = new NativeArray<byte>(newBonesPerVertex.ToArray(), Allocator.Temp))
        using (var nativeBoneWeights = new NativeArray<BoneWeight1>(newBoneWeights.ToArray(), Allocator.Temp))
        {
            newMesh.SetBoneWeights(nativeBonesPerVertex, nativeBoneWeights);
        }

        Undo.RecordObject(skinnedMesh, "Normalize Bone Weights");
        skinnedMesh.sharedMesh = newMesh;
    }
    
    public static void MirrorBone(Transform bone, Transform rootBone, bool mirrorX = true, bool mirrorY = false, bool mirrorZ = false)
    {
        if (bone == null || rootBone == null)
            return;
            
        Undo.RecordObject(bone, "Mirror Bone");
        
        Vector3 localPos = bone.localPosition;
        if (mirrorX) localPos.x = -localPos.x;
        if (mirrorY) localPos.y = -localPos.y;
        if (mirrorZ) localPos.z = -localPos.z;
        
        bone.localPosition = localPos;
        
        // Mirror rotation
        Vector3 localEuler = bone.localEulerAngles;
        if (mirrorX) localEuler.y = -localEuler.y;
        if (mirrorY) localEuler.x = -localEuler.x;
        if (mirrorZ) localEuler.z = -localEuler.z;
        
        bone.localEulerAngles = localEuler;
    }
    
    public static List<Transform> GetBoneHierarchy(Transform rootBone)
    {
        List<Transform> hierarchy = new List<Transform>();
        CollectBoneHierarchy(rootBone, hierarchy);
        return hierarchy;
    }
    
    private static void CollectBoneHierarchy(Transform bone, List<Transform> hierarchy)
    {
        hierarchy.Add(bone);
        for (int i = 0; i < bone.childCount; i++)
        {
            CollectBoneHierarchy(bone.GetChild(i), hierarchy);
        }
    }
    
    public static void AlignBoneToChild(Transform bone)
    {
        if (bone == null || bone.childCount == 0)
            return;
            
        Undo.RecordObject(bone, "Align Bone to Child");
        
        Transform child = bone.GetChild(0);
        Vector3 direction = (child.position - bone.position).normalized;
        
        if (direction != Vector3.zero)
        {
            bone.rotation = Quaternion.LookRotation(direction, bone.up);
        }
    }
    
    public static void ResetBoneTransform(Transform bone)
    {
        if (bone == null)
            return;
            
        Undo.RecordObject(bone, "Reset Bone Transform");
        
        bone.localPosition = Vector3.zero;
        bone.localRotation = Quaternion.identity;
        bone.localScale = Vector3.one;
    }
}
#endif
