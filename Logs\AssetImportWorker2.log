Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.22f1 (887be4894c44) revision 8944612'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Core' Language: 'en' Physical Memory: 24499 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Unity versions\Editor\2022.3.22f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/VRC Projects/Crust test
-logFile
Logs/AssetImportWorker2.log
-srvPort
59114
Successfully changed project path to: D:/VRC Projects/Crust test
D:/VRC Projects/Crust test
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [11120] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2842668808 [EditorId] 2842668808 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-K0NST4K) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [11120] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2842668808 [EditorId] 2842668808 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-K0NST4K) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with 11 workers.
Refreshing native plugins compatible for Editor in 24.85 ms, found 5 plugins.
Preloading 2 native plugins for Editor in 0.91 ms.
Initialize engine version: 2022.3.22f1 (887be4894c44)
[Subsystems] Discovering subsystems at path D:/Unity versions/Editor/2022.3.22f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/VRC Projects/Crust test/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'D:/Unity versions/Editor/2022.3.22f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity versions/Editor/2022.3.22f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity versions/Editor/2022.3.22f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56972
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity versions/Editor/2022.3.22f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Unity versions/Editor/2022.3.22f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity versions/Editor/2022.3.22f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005852 seconds.
- Loaded All Assemblies, in  0.309 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
