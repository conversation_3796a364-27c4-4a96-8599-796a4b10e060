{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20752, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20752, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20752, "tid": 2347, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20752, "tid": 2347, "ts": 1753725006884138, "dur": 604, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20752, "tid": 2347, "ts": 1753725006888709, "dur": 630, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20752, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20752, "tid": 1, "ts": 1753725006624242, "dur": 4538, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753725006628783, "dur": 24929, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753725006653721, "dur": 41139, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20752, "tid": 2347, "ts": 1753725006889342, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 20752, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006622432, "dur": 10428, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006632862, "dur": 243296, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006633884, "dur": 2211, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006636100, "dur": 1313, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637416, "dur": 144, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637563, "dur": 11, "ph": "X", "name": "ProcessMessages 20517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637576, "dur": 26, "ph": "X", "name": "ReadAsync 20517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637605, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637607, "dur": 18, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637628, "dur": 36, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637667, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637686, "dur": 73, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637761, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637787, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637789, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637810, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637828, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637829, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637848, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637866, "dur": 15, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637883, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637902, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637921, "dur": 16, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637940, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637958, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637960, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637982, "dur": 13, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006637998, "dur": 16, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638016, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638035, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638053, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638072, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638091, "dur": 16, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638109, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638111, "dur": 16, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638128, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638130, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638150, "dur": 17, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638170, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638186, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638188, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638205, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638207, "dur": 432, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638641, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638642, "dur": 87, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638731, "dur": 5, "ph": "X", "name": "ProcessMessages 8259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638737, "dur": 21, "ph": "X", "name": "ReadAsync 8259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638760, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638762, "dur": 29, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638795, "dur": 20, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638819, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638837, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638857, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638859, "dur": 18, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638879, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638881, "dur": 18, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638902, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638923, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638943, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638961, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638981, "dur": 4, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006638986, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639006, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639026, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639047, "dur": 15, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639065, "dur": 14, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639082, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639098, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639100, "dur": 16, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639118, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639139, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639140, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639164, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639181, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639200, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639218, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639238, "dur": 14, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639254, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639276, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639279, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639298, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639326, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639352, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639354, "dur": 18, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639376, "dur": 16, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639395, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639418, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639419, "dur": 24, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639446, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639449, "dur": 17, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639470, "dur": 18, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639492, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639513, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639533, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639552, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639571, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639587, "dur": 18, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639608, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639630, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639651, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639674, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639693, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639719, "dur": 20, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639742, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639765, "dur": 13, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639780, "dur": 43, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639827, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639848, "dur": 16, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639867, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639888, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639908, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639910, "dur": 16, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639929, "dur": 15, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639948, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639968, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006639987, "dur": 13, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640004, "dur": 21, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640028, "dur": 20, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640049, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640051, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640071, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640072, "dur": 18, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640093, "dur": 16, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640112, "dur": 11, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640124, "dur": 15, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640142, "dur": 18, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640163, "dur": 17, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640182, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640184, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640206, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640230, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640250, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640271, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640290, "dur": 16, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640309, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640330, "dur": 20, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640352, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640371, "dur": 17, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640391, "dur": 20, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640413, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640415, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640436, "dur": 18, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640458, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640481, "dur": 13, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640498, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640515, "dur": 19, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640535, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640537, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640557, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640578, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640598, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640613, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640640, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640659, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640677, "dur": 16, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640696, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640720, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640738, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640739, "dur": 14, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640756, "dur": 42, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640800, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640802, "dur": 15, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640820, "dur": 16, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640838, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640863, "dur": 20, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640885, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640887, "dur": 24, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640915, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640935, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640937, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640954, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006640983, "dur": 31, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641017, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641019, "dur": 25, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641047, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641049, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641077, "dur": 18, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641097, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641099, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641120, "dur": 18, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641142, "dur": 24, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641168, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641170, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641196, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641198, "dur": 15, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641215, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641217, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641244, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641265, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641282, "dur": 14, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641299, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641300, "dur": 20, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641324, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641344, "dur": 14, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641361, "dur": 15, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641378, "dur": 13, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641394, "dur": 17, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641414, "dur": 13, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641430, "dur": 16, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641448, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641469, "dur": 15, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641487, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641528, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641546, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641565, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641567, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641587, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641589, "dur": 57, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641649, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641673, "dur": 15, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641691, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641711, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641731, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641749, "dur": 18, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641770, "dur": 14, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641787, "dur": 18, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641808, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641854, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641875, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641891, "dur": 17, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641910, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641930, "dur": 40, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641972, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006641994, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642013, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642032, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642053, "dur": 63, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642118, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642119, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642143, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642145, "dur": 28, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642176, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642197, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642199, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642220, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642222, "dur": 14, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642239, "dur": 20, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642263, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642284, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642304, "dur": 13, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642320, "dur": 1, "ph": "X", "name": "ProcessMessages 55", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642321, "dur": 15, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642339, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642387, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642405, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642408, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642427, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642443, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642447, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642488, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642507, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642530, "dur": 11, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642543, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642595, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642616, "dur": 15, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642632, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642634, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642653, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642705, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642722, "dur": 16, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642741, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642763, "dur": 47, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642813, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642831, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642851, "dur": 18, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642871, "dur": 40, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642915, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642936, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642937, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642958, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006642975, "dur": 46, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643025, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643047, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643069, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643093, "dur": 13, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643109, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643147, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643167, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643187, "dur": 16, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643205, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643247, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643266, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643288, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643307, "dur": 34, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643343, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643362, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643382, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643384, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643402, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643444, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643464, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643466, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643487, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643504, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643550, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643572, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643594, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643612, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643650, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643672, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643691, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643714, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643754, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643773, "dur": 12, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643789, "dur": 14, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643806, "dur": 13, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643821, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643823, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643858, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643880, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643898, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643916, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643918, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643936, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643939, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643959, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643961, "dur": 12, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643974, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006643992, "dur": 12, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644005, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644025, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644037, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644087, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644110, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644129, "dur": 14, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644145, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644163, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644183, "dur": 16, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644218, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644236, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644238, "dur": 15, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644255, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644302, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644324, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644344, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644366, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644385, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644407, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644429, "dur": 18, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644448, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644453, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644497, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644517, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644538, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644558, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644579, "dur": 15, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644597, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644615, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644634, "dur": 11, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644648, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644699, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644731, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644732, "dur": 25, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644758, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644760, "dur": 16, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644779, "dur": 16, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644797, "dur": 18, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644818, "dur": 15, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644835, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644837, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644855, "dur": 14, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644871, "dur": 34, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644908, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644928, "dur": 17, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644947, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644948, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644966, "dur": 15, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006644984, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645002, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645021, "dur": 14, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645038, "dur": 15, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645056, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645142, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645161, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645182, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645203, "dur": 15, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645221, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645222, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645241, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645262, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645277, "dur": 21, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645302, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645354, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645378, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645380, "dur": 31, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645412, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645414, "dur": 17, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645434, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645435, "dur": 16, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645454, "dur": 18, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645475, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645493, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645519, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645521, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645555, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645591, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645614, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645616, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645638, "dur": 18, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645659, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645661, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645687, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645712, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645735, "dur": 13, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645750, "dur": 15, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645767, "dur": 53, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645824, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645843, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645844, "dur": 24, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645870, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645871, "dur": 13, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645886, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645906, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645925, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645927, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645950, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645968, "dur": 16, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006645988, "dur": 14, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646009, "dur": 36, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646047, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646066, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646085, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646103, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646122, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646139, "dur": 16, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646158, "dur": 218, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646379, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646380, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646405, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646407, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646442, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646470, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646472, "dur": 24, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646497, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646499, "dur": 17, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646518, "dur": 12, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646532, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646551, "dur": 25, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646578, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646581, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646600, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646621, "dur": 19, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646642, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646644, "dur": 70, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646718, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646747, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006646749, "dur": 319, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647071, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647073, "dur": 41, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647117, "dur": 3, "ph": "X", "name": "ProcessMessages 2127", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647121, "dur": 27, "ph": "X", "name": "ReadAsync 2127", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647151, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647153, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647230, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647282, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647284, "dur": 26, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647312, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647314, "dur": 109, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647428, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647469, "dur": 28, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647500, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647502, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647537, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647540, "dur": 29, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647570, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647572, "dur": 25, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647600, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647604, "dur": 28, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647634, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647636, "dur": 25, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647664, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647667, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647696, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647700, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647788, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647819, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647822, "dur": 35, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647860, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647862, "dur": 63, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647929, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647963, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647966, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647995, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006647998, "dur": 30, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648030, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648032, "dur": 30, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648065, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648067, "dur": 26, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648096, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648098, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648121, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648145, "dur": 96, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648245, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648276, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648278, "dur": 20, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648300, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648302, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648321, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648365, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648390, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648412, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648434, "dur": 63, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648501, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648526, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648551, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648553, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648572, "dur": 42, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648618, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648640, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648659, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648678, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648701, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648723, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648725, "dur": 27, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648754, "dur": 15, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648772, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648823, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648838, "dur": 12, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648852, "dur": 15, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648869, "dur": 19, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648891, "dur": 16, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648911, "dur": 39, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648953, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006648979, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649002, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649005, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649027, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649048, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649068, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649069, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649093, "dur": 14, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649110, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649126, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649128, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649177, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649194, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649215, "dur": 13, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649231, "dur": 16, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649250, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649269, "dur": 15, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649287, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649306, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649325, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649345, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649395, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649413, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649431, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649449, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649452, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649471, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649473, "dur": 17, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649492, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649494, "dur": 15, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649511, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649532, "dur": 14, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649549, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649565, "dur": 14, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649582, "dur": 13, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649599, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649639, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649658, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649680, "dur": 14, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649696, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649748, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649770, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649788, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649805, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649823, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649824, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649843, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649862, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649879, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649899, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649950, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649969, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006649988, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650007, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650025, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650063, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650084, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650087, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650108, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650126, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650144, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650162, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650183, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650200, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650217, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650266, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650286, "dur": 14, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650302, "dur": 14, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650318, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650341, "dur": 15, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650359, "dur": 15, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650376, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650378, "dur": 15, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650395, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650411, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650414, "dur": 12, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650429, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650469, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650487, "dur": 14, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650503, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650505, "dur": 16, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650523, "dur": 17, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650542, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650560, "dur": 15, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650579, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650599, "dur": 15, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650617, "dur": 13, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650634, "dur": 35, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650672, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650693, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650712, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650715, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650736, "dur": 13, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650751, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650767, "dur": 16, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650785, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650787, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650803, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650822, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650840, "dur": 38, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650883, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650904, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650924, "dur": 15, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650942, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650962, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650964, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006650983, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651011, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651013, "dur": 11, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651025, "dur": 13, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651041, "dur": 43, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651086, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651110, "dur": 16, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651128, "dur": 16, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651146, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651171, "dur": 16, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651190, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651211, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651230, "dur": 15, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651247, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651295, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651314, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651334, "dur": 15, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651350, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651352, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651373, "dur": 14, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651391, "dur": 17, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651411, "dur": 59, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651472, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651492, "dur": 14, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651508, "dur": 15, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651526, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651538, "dur": 58, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651598, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651623, "dur": 17, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651642, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651660, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651678, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651696, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651719, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651737, "dur": 14, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651754, "dur": 16, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651772, "dur": 14, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651789, "dur": 46, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651837, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651859, "dur": 16, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651878, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651880, "dur": 15, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651898, "dur": 15, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651914, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651933, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651953, "dur": 11, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651966, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651982, "dur": 12, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006651995, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652018, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652042, "dur": 38, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652082, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652103, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652123, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652144, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652147, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652173, "dur": 20, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652195, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652213, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652233, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652252, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652255, "dur": 12, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652270, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652310, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652330, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652349, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652369, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652387, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652405, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652423, "dur": 14, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652439, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652456, "dur": 14, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652472, "dur": 39, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652514, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652531, "dur": 22, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652556, "dur": 15, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652574, "dur": 15, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652592, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652609, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652627, "dur": 15, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652645, "dur": 13, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652660, "dur": 18, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652680, "dur": 12, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652695, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652738, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652758, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652777, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652796, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652816, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652834, "dur": 15, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652852, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652868, "dur": 13, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652883, "dur": 14, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652899, "dur": 39, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652941, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652960, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652980, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006652997, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653016, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653033, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653052, "dur": 14, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653068, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653085, "dur": 13, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653100, "dur": 39, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653142, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653158, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653160, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653185, "dur": 15, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653203, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653224, "dur": 15, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653242, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653260, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653277, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653297, "dur": 13, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653312, "dur": 40, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653354, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653372, "dur": 15, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653389, "dur": 19, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653411, "dur": 14, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653427, "dur": 15, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653445, "dur": 15, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653463, "dur": 15, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653480, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653498, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653514, "dur": 14, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653530, "dur": 39, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653571, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653589, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653607, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653625, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653641, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653643, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653661, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653677, "dur": 15, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653695, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653715, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653734, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653783, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653801, "dur": 104, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653910, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006653934, "dur": 281, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654218, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654259, "dur": 8, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654268, "dur": 27, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654298, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654301, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654322, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654325, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654349, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654351, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654369, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654372, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654389, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654391, "dur": 17, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654411, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654412, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654430, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654432, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654444, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654462, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654464, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654484, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654503, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654504, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654526, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654528, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654547, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654549, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654568, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654582, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654584, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654603, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654605, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654624, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654626, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654643, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654644, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654663, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654664, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654682, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654685, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654706, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654708, "dur": 16, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654726, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654728, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654748, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654750, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654769, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654787, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654789, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654806, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654807, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654825, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654843, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654845, "dur": 17, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654864, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654866, "dur": 16, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654884, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654886, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654906, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654908, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654927, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654929, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654953, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654955, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654980, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006654983, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655006, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655009, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655030, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655032, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655052, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655054, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655078, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655081, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655102, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655104, "dur": 20, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655126, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655128, "dur": 20, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655151, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655153, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655171, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655173, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655193, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655212, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655214, "dur": 34, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655250, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655253, "dur": 21, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655276, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655278, "dur": 17, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655299, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655301, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655323, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655325, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655342, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655344, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655364, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655396, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655398, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655422, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655425, "dur": 20, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655447, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655449, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655464, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655466, "dur": 13, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655482, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655500, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655501, "dur": 12, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655517, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655535, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655554, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655555, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655574, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655589, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655611, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655613, "dur": 13, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006655628, "dur": 3124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006658757, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006658792, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006658794, "dur": 511, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659309, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659327, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659344, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659487, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659513, "dur": 395, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659911, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659936, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659957, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006659997, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660013, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660276, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660291, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660314, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660442, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660457, "dur": 10, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660469, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660485, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660509, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660524, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660581, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660597, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660612, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660629, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660647, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660730, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660744, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660779, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660791, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660860, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660875, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660912, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660930, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660942, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660956, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660984, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006660998, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661059, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661069, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661087, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661105, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661117, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661133, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661158, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661172, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661188, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661211, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661229, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661392, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661403, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661434, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661448, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661500, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661518, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661536, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661566, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661583, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661599, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661668, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661685, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661701, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661730, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661750, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661770, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661792, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661807, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661823, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661843, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661866, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661868, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661883, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661925, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661942, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661977, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006661994, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662031, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662050, "dur": 93, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662147, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662161, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662203, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662217, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662268, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662284, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662351, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662367, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662384, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662405, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662417, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662481, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662497, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662520, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662541, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662542, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662606, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662620, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662637, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662651, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662688, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662704, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662725, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662763, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662778, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662792, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662841, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662858, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662875, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662908, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662922, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662983, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006662999, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663017, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663040, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663101, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663132, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663153, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663154, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663174, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663249, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663273, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663343, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663360, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663390, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663404, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663419, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663444, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663473, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663492, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663597, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663611, "dur": 119, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663734, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663758, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663783, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663796, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663834, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663848, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663968, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006663981, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664013, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664033, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664051, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664063, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664077, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664170, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664191, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664213, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664226, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664242, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664275, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664291, "dur": 558, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664852, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664880, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664884, "dur": 33, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664919, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664935, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664961, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006664975, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665061, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665084, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665085, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665144, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665158, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665263, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665277, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665403, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665418, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665435, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665487, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665503, "dur": 226, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665733, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665754, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665775, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665860, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665880, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665901, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665921, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665923, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665943, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665945, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665962, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006665990, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666006, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666070, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666093, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666134, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666149, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666284, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666305, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666324, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666364, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666383, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666385, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666409, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666427, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666445, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666470, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666486, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666855, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006666881, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006667099, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006667122, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006667338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006667341, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006667361, "dur": 45669, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006713041, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006713047, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006713075, "dur": 2816, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006715896, "dur": 728, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006716629, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006716646, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006716648, "dur": 503, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717157, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717171, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717405, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717420, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717422, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717460, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717473, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717549, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006717561, "dur": 435, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718001, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718017, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718037, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718054, "dur": 600, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718658, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718673, "dur": 296, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718972, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006718984, "dur": 462, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719450, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719466, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719483, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719500, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719524, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719539, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719746, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006719772, "dur": 628, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720403, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720413, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720433, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720560, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720575, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720895, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006720907, "dur": 143, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721055, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721070, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721088, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721264, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721277, "dur": 539, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721820, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006721833, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722108, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722119, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722179, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722194, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722274, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722289, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722331, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722342, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722378, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722393, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722553, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722567, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722595, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722618, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722857, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006722881, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723236, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723254, "dur": 374, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723633, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723660, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723662, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723688, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723702, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723763, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723779, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723975, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006723991, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724006, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724057, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724071, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724327, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724345, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724347, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724656, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724659, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724671, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724779, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724799, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724821, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724836, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724854, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724867, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724882, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724912, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724930, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724951, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724965, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724976, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006724994, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725011, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725033, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725056, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725057, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725070, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725082, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725097, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725116, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725118, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725134, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725136, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725153, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725155, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725174, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725193, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725195, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725213, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725215, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725234, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725236, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725252, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725253, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725271, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725294, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725297, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725339, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725437, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725459, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725480, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725482, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725503, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725504, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725523, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725542, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725566, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725567, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725589, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725591, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725615, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725617, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725641, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725643, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725662, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725664, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725683, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725702, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725718, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725737, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725752, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725768, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725770, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725785, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725801, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725820, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725841, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006725862, "dur": 189, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726055, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726079, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726081, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726104, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726132, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726151, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726257, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726290, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726292, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726326, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006726328, "dur": 139729, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006866066, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006866070, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006866120, "dur": 17, "ph": "X", "name": "ReadAsync 7081", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006866140, "dur": 9453, "ph": "X", "name": "ProcessMessages 13845", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006875598, "dur": 58, "ph": "X", "name": "ReadAsync 13845", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006875659, "dur": 304, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753725006875966, "dur": 142, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 20752, "tid": 2347, "ts": 1753725006889353, "dur": 1366, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 8589934592, "ts": 1753725006620066, "dur": 74830, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 8589934592, "ts": 1753725006694900, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20752, "tid": 8589934592, "ts": 1753725006694908, "dur": 1098, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 2347, "ts": 1753725006890722, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20752, "tid": 4294967296, "ts": 1753725006606142, "dur": 271154, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753725006609556, "dur": 5915, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753725006877314, "dur": 4412, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753725006880023, "dur": 47, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753725006881794, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20752, "tid": 2347, "ts": 1753725006890732, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753725006631125, "dur": 1775, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006632914, "dur": 798, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006633830, "dur": 63, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753725006633894, "dur": 278, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006634980, "dur": 965, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_DDB1499AED0A17A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753725006636866, "dur": 1044, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753725006638996, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753725006634194, "dur": 19940, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006654150, "dur": 214316, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006868467, "dur": 248, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006870458, "dur": 96, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006870592, "dur": 1221, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753725006634472, "dur": 19684, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006654173, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006654527, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006654746, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006655092, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006655660, "dur": 737, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006658078, "dur": 383, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753725006654863, "dur": 4197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006659119, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006659326, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006659528, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006659763, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006660262, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006660802, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006660927, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006661394, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006661558, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006662102, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006662226, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006662824, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006662956, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006663187, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006663359, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006663427, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006663595, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006664450, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006664560, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006664623, "dur": 687, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006665372, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006663684, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006665724, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006666072, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006666201, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725006666335, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006666805, "dur": 48571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006716128, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Features.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725006715378, "dur": 1543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006716955, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006718383, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006719831, "dur": 1387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006721245, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006722665, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.harmony-patches.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753725006725038, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006725186, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006725517, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006725744, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753725006726148, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725006726442, "dur": 142041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006634514, "dur": 19665, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006654182, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725006654546, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725006654544, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_5A104ACC7C39F2CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725006655707, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17874695840298502191.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753725006655957, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006656267, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006656486, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006656706, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006656908, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006657134, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006657524, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006657722, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006657936, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006658522, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006658722, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006658942, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006659174, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006659360, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006659550, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006659926, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006660264, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006660664, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725006660855, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006661467, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006662034, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725006662272, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725006662195, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006663055, "dur": 751, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006663811, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006664500, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006664578, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006665074, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006665372, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725006665302, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006666108, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725006666225, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006666736, "dur": 48643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006715640, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725006715380, "dur": 1545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006716966, "dur": 1388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006718376, "dur": 7700, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725006726166, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725006726633, "dur": 141860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006634489, "dur": 19677, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006654175, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006654535, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006654871, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753725006655184, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753725006655946, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006656254, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006656473, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006656687, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006656881, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006657099, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006657384, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006657860, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006658057, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006658636, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006659084, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006659275, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006659493, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006659861, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006660260, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006660662, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006660819, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006660976, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006661125, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006661256, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006661738, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006661909, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006662277, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753725006663389, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\ManipulationsTimeline.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753725006663452, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\Manipulator.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753725006662032, "dur": 1683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006663715, "dur": 677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006664417, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006664550, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006665087, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006665140, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725006665267, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006665740, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006666157, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006666269, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006666810, "dur": 51576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006718739, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753725006718387, "dur": 1428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006719870, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.vrchat.core.vpm-resolver.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006721396, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006722962, "dur": 1691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725006724653, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006725261, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006725617, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725006726056, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.DOTween.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753725006726192, "dur": 142275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725006634508, "dur": 19665, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725006654193, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725006654176, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006654536, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725006654747, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006655267, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725006655641, "dur": 755, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725006656398, "dur": 725, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725006657280, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725006658073, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753725006658455, "dur": 494, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItemDataHolder.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753725006654855, "dur": 4755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006659671, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006659823, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006660275, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006660340, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006660652, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006660784, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006661275, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725006661473, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006662377, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006662764, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006663254, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006663357, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725006663777, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725006663735, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006664175, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725006665082, "dur": 104, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725006866346, "dur": 99, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725006665273, "dur": 201436, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006634531, "dur": 19655, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006654189, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725006654280, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725006654338, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725006654337, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D63E2E2896B4E5B3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725006654734, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006655427, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753725006655590, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753725006655950, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006656269, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006656481, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006656679, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006656880, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006657071, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006657813, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006658020, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006658616, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006658824, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006659049, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006659245, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006659480, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006659758, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006660261, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006660655, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725006661254, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Audio\\AudioMixerProperties.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753725006660795, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006661390, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006661873, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725006662454, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725006662722, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725006662848, "dur": 350, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Dependencies\\librsync\\librsync.net.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725006662382, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006663265, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006663674, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006663788, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006664181, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006664384, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725006664355, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006665010, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006665486, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006666196, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006666292, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006666704, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006667190, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006667434, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006667676, "dur": 48795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006716473, "dur": 1394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006717896, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006719330, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006720775, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006722159, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006723579, "dur": 1385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006724999, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725006726411, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725006726468, "dur": 141994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006634548, "dur": 19645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006654196, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D2E6889350BDFFF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006654543, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006654541, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8C5E2A3FA2C2B638.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006655004, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753725006655939, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006656267, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006656487, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006656695, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006656885, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006657117, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006657347, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006657546, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006657752, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006657967, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006658545, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006658749, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006658955, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006659166, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006659375, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006659762, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006660260, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006660657, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006660796, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006661298, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006660791, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006661769, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006661843, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006662187, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006662547, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006662719, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006662696, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006663355, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006663481, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725006663777, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006663940, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006664219, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006664339, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006663590, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006664579, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006665263, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006665765, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006666266, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006666750, "dur": 48631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006715383, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006716952, "dur": 545, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006717500, "dur": 1468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006718998, "dur": 1572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006720571, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725006720908, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006722454, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006723970, "dur": 428, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006724439, "dur": 452, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725006724436, "dur": 2128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725006726606, "dur": 141874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006634564, "dur": 19637, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006654205, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_DD36A0C7D3C71D14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725006654324, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725006654322, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8B6BDD851B5DD076.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725006654895, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006654949, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006655344, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753725006655943, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006656251, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006656458, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006656666, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006656868, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006657069, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006657280, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006657490, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006657632, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006657841, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006658037, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006658613, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006658812, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006659025, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006659231, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006659415, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006659611, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006659834, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006660278, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006660680, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725006660821, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006661285, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006661498, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725006661611, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006661912, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006662710, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006662864, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725006663656, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Editor\\EditorCore\\Lightmapping.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753725006663782, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Editor\\EditorCore\\LightmapUVEditor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753725006662986, "dur": 1145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006664131, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006664194, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006664462, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006665080, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006665507, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006665829, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006666261, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006666647, "dur": 50314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006716963, "dur": 1344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006718343, "dur": 1352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006719696, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006719818, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006720996, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006721252, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.param-introspection.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006722721, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006724106, "dur": 1431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRLabs.AV3Manager.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725006725657, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Management.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725006726003, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725006726062, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/nadena.dev.modular-avatar.core.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753725006726198, "dur": 142266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006634587, "dur": 19624, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006654228, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006654215, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_ED9976D1F71D37ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006654532, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006654531, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_48F6D28C1C558B25.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006655440, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753725006655568, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753725006655777, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006655837, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5004070446096500560.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753725006655948, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006656316, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006656548, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006656740, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006656934, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006657156, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006657358, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006657551, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006657799, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006658001, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006658582, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006658790, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006659025, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006659223, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006659419, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006659521, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006659790, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006660285, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006660661, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006660807, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006661405, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006661534, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006662151, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006662250, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006662852, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.core.vpm-resolver\\Editor\\Dependencies\\YamlDotNet.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006662333, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006662965, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006663069, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006663203, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006663329, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006664094, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725006664557, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006664307, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006665038, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006665182, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006665372, "dur": 900, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006665094, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006666654, "dur": 48729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006715385, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006716965, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006718386, "dur": 1386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006719798, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006721243, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006722556, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006722617, "dur": 1626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006724244, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725006724794, "dur": 594, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006725583, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006725890, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725006724324, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725006726634, "dur": 141856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006634607, "dur": 19612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006654222, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CABBC8F7A32A378E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725006654538, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725006654537, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_917E5BD489C19DA6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725006655105, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006655318, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753725006655637, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006655722, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753725006655945, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006656250, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006656459, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006656676, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006656894, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006657118, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006657595, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006657812, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006658020, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006658600, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006658909, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\EditorClip.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753725006658815, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006659602, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006659909, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006660267, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006660659, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725006660784, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006661265, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006661926, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006662190, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725006662331, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725006662722, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725006662488, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006663124, "dur": 825, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006663952, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006664438, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006664563, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006665372, "dur": 922, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725006665041, "dur": 1331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006666412, "dur": 29940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006696354, "dur": 2557, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006698912, "dur": 17369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006716283, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006717754, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006719188, "dur": 898, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725006720117, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006721605, "dur": 1550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006723225, "dur": 1411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.ExampleCentral.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006725892, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725006724669, "dur": 1696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725006726437, "dur": 142028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006634624, "dur": 19602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006654536, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725006654535, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006654819, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753725006655284, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006655532, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753725006655941, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006656247, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006656458, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006656671, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006656869, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006657096, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006657285, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006657503, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006657792, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Editor\\EditorCore\\VertexOnFaceEditor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753725006657696, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006658592, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006658908, "dur": 855, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Sequence\\SelectAndMoveItem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753725006658811, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006659864, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006660263, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006660657, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006660794, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006661329, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006661801, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725006661458, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006662060, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006662322, "dur": 406, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006662747, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006662877, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006662992, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006663129, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006663514, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006663954, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725006664125, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006664606, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006664663, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006665102, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006665604, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006666041, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006666478, "dur": 32435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006698914, "dur": 17361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006716279, "dur": 1490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006717809, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006719316, "dur": 1398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006720748, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006722161, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006722522, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006724006, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725006725624, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006725678, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/nadena.dev.modular-avatar.core.editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725006726055, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753725006726146, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725006726466, "dur": 142021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006634650, "dur": 19583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006654540, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725006654539, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F97977208FF60E8C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006655426, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753725006655955, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006656265, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006656469, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006656684, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006656884, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006657098, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006657345, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006657536, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006657731, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006657954, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006658529, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006658738, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006658953, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006659173, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006659376, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006659567, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006659889, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006660269, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006660661, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006660810, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006660941, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006661076, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006661488, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725006661203, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006661693, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006661779, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006661930, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006662497, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006662613, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006662731, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006662860, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006662973, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663110, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663262, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663386, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663510, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663616, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663778, "dur": 430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725006664385, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725006663754, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006664819, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006664908, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006665344, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006665401, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006665831, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006666226, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006666639, "dur": 48746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006715387, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006716959, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/FbxBuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006718381, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006719823, "dur": 1405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006721229, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006721428, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006722893, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.vrchat.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006724352, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006725702, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725006726152, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725006726612, "dur": 141862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006634661, "dur": 19577, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006654239, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AF9F947519E8CC62.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725006654539, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006655937, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006656263, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006656503, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006656711, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006656904, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006657157, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006657439, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006657636, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006657866, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006658478, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006658674, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006658875, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006659088, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006659331, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006659540, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006659941, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006660262, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006660657, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725006660798, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006661551, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006662152, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725006662267, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725006662380, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006662971, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725006663473, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753725006663108, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006663707, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1753725006664166, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006664228, "dur": 98, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006665302, "dur": 48061, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1753725006715377, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006716954, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006718360, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006719792, "dur": 1420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006721240, "dur": 1401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.ShaderStripping.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006722675, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006724031, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725006725472, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725006725660, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/nadena.dev.modular-avatar.core.editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753725006726060, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753725006726187, "dur": 142282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725006874775, "dur": 1399, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20752, "tid": 2347, "ts": 1753725006891081, "dur": 1756, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20752, "tid": 2347, "ts": 1753725006892875, "dur": 1563, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20752, "tid": 2347, "ts": 1753725006887219, "dur": 7856, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}