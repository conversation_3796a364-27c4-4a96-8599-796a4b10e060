{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20752, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20752, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20752, "tid": 998, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20752, "tid": 998, "ts": 1753721829610765, "dur": 18, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20752, "tid": 998, "ts": 1753721829610797, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20752, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20752, "tid": 1, "ts": 1753721827711154, "dur": 2537, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753721827713696, "dur": 29679, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753721827743381, "dur": 106820, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20752, "tid": 998, "ts": 1753721829610803, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 20752, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827711103, "dur": 89913, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827801017, "dur": 1809199, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827801174, "dur": 438, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827801616, "dur": 681, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827802300, "dur": 349, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827802896, "dur": 18, "ph": "X", "name": "ProcessMessages 16267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827803132, "dur": 405, "ph": "X", "name": "ReadAsync 16267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827803769, "dur": 11, "ph": "X", "name": "ProcessMessages 10915", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827803941, "dur": 159, "ph": "X", "name": "ReadAsync 10915", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827804249, "dur": 6, "ph": "X", "name": "ProcessMessages 6255", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827804394, "dur": 165, "ph": "X", "name": "ReadAsync 6255", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827804562, "dur": 5, "ph": "X", "name": "ProcessMessages 5471", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827804710, "dur": 156, "ph": "X", "name": "ReadAsync 5471", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827804869, "dur": 5, "ph": "X", "name": "ProcessMessages 5008", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827805012, "dur": 174, "ph": "X", "name": "ReadAsync 5008", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827805188, "dur": 5, "ph": "X", "name": "ProcessMessages 5945", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827805336, "dur": 216, "ph": "X", "name": "ReadAsync 5945", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827805816, "dur": 4, "ph": "X", "name": "ProcessMessages 5748", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827805821, "dur": 234, "ph": "X", "name": "ReadAsync 5748", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806057, "dur": 6, "ph": "X", "name": "ProcessMessages 8307", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806246, "dur": 248, "ph": "X", "name": "ReadAsync 8307", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806497, "dur": 3, "ph": "X", "name": "ProcessMessages 4693", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806501, "dur": 144, "ph": "X", "name": "ReadAsync 4693", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806647, "dur": 2, "ph": "X", "name": "ProcessMessages 2595", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806650, "dur": 242, "ph": "X", "name": "ReadAsync 2595", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806894, "dur": 2, "ph": "X", "name": "ProcessMessages 2773", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827806896, "dur": 133, "ph": "X", "name": "ReadAsync 2773", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827807032, "dur": 1, "ph": "X", "name": "ProcessMessages 1683", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827807034, "dur": 29, "ph": "X", "name": "ReadAsync 1683", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827839321, "dur": 14, "ph": "X", "name": "ProcessMessages 1889", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827839338, "dur": 1467, "ph": "X", "name": "ReadAsync 1889", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827840809, "dur": 24, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827840835, "dur": 101, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827840940, "dur": 6, "ph": "X", "name": "ProcessMessages 5963", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827840948, "dur": 91, "ph": "X", "name": "ReadAsync 5963", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841044, "dur": 5, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841051, "dur": 29, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841081, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841083, "dur": 26, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841113, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841115, "dur": 27, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841146, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841149, "dur": 29, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841179, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841181, "dur": 27, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841211, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841213, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841242, "dur": 23, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841271, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841328, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841375, "dur": 2, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841378, "dur": 15, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841396, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841419, "dur": 27, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841449, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841450, "dur": 19, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841558, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841591, "dur": 1, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841593, "dur": 26, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841620, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841622, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841648, "dur": 23, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841673, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841675, "dur": 24, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841703, "dur": 22, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841726, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841729, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841754, "dur": 18, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841774, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841777, "dur": 37, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841819, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841841, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841843, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841868, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841871, "dur": 30, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841902, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841904, "dur": 23, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841929, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841932, "dur": 35, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841970, "dur": 2, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841973, "dur": 21, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827841997, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842064, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842090, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842092, "dur": 25, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842119, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842121, "dur": 21, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842143, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842145, "dur": 20, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842169, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842195, "dur": 17, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842215, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842238, "dur": 44, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842285, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842310, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842311, "dur": 29, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842343, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842346, "dur": 27, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842376, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842398, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842421, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842424, "dur": 17, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842446, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842467, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842469, "dur": 129, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842600, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842627, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842630, "dur": 26, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842658, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842660, "dur": 26, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842688, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842690, "dur": 27, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842719, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842721, "dur": 26, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842750, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842752, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842775, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842796, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842798, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842866, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842892, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842894, "dur": 26, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842922, "dur": 22, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842947, "dur": 17, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842967, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842969, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827842991, "dur": 15, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843009, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843027, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843045, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843071, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843090, "dur": 77, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843170, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843189, "dur": 16, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843207, "dur": 14, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843223, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843241, "dur": 15, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843259, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843276, "dur": 15, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843293, "dur": 19, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843314, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843331, "dur": 19, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843352, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843371, "dur": 81, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843455, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843476, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843478, "dur": 19, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843501, "dur": 31, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843534, "dur": 20, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843557, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843580, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843601, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843604, "dur": 15, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843622, "dur": 17, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843641, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843699, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843731, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843733, "dur": 22, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843757, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843760, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843782, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843784, "dur": 23, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843810, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843812, "dur": 24, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843837, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843839, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843862, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827843864, "dur": 162, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844030, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844048, "dur": 46, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844097, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844121, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844122, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844146, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844148, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844173, "dur": 29, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844205, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844207, "dur": 20, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844231, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844254, "dur": 12, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844269, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844324, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844351, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844353, "dur": 27, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844383, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844407, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844430, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844431, "dur": 21, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844455, "dur": 17, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844475, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844494, "dur": 17, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844513, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844572, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844598, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844599, "dur": 16, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844618, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844637, "dur": 48, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844689, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844710, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844712, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844735, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844737, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844762, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844765, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844787, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844789, "dur": 26, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844817, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844818, "dur": 18, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844839, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844860, "dur": 15, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844878, "dur": 44, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844926, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844948, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844950, "dur": 22, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844975, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844978, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827844998, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845041, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845063, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845065, "dur": 18, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845086, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845087, "dur": 22, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845112, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845113, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845136, "dur": 18, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845158, "dur": 23, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845183, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845185, "dur": 21, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845208, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845210, "dur": 14, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845226, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845291, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845323, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845324, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845349, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845369, "dur": 26, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845398, "dur": 23, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845423, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845427, "dur": 64, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845495, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845525, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845527, "dur": 25, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845554, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845556, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845579, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845662, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845687, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845712, "dur": 1, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845714, "dur": 28, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845745, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845747, "dur": 64, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845815, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845842, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845844, "dur": 21, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845868, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845869, "dur": 42, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845913, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845916, "dur": 39, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845957, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845959, "dur": 22, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845984, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827845987, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846011, "dur": 17, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846031, "dur": 36, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846070, "dur": 43, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846117, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846145, "dur": 15, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846163, "dur": 27, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846193, "dur": 15, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846210, "dur": 43, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846256, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846275, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846293, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846312, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846314, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846331, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846350, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846368, "dur": 14, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846385, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846403, "dur": 15, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846420, "dur": 51, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846474, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846499, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846501, "dur": 15, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846519, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846538, "dur": 16, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846558, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846579, "dur": 15, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846597, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846616, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846632, "dur": 17, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846652, "dur": 13, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846667, "dur": 40, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846709, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846728, "dur": 15, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846746, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846764, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846784, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846803, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846821, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846838, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846858, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846874, "dur": 16, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846892, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846953, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846978, "dur": 18, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827846999, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847017, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847071, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847095, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847114, "dur": 23, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847138, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847139, "dur": 15, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847156, "dur": 14, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847173, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847191, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847209, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847224, "dur": 14, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847241, "dur": 48, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847292, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847309, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847328, "dur": 16, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847346, "dur": 59, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847407, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847426, "dur": 9, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847437, "dur": 14, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847454, "dur": 16, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847473, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847491, "dur": 14, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847508, "dur": 16, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847527, "dur": 14, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847543, "dur": 16, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847562, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847577, "dur": 14, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847593, "dur": 20, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847615, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847632, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847706, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847723, "dur": 15, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847740, "dur": 16, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847759, "dur": 15, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847776, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847793, "dur": 15, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847811, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847828, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847845, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847863, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847887, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847904, "dur": 79, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827847985, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848005, "dur": 14, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848023, "dur": 14, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848038, "dur": 15, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848055, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848056, "dur": 15, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848073, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848091, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848108, "dur": 14, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848124, "dur": 15, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848141, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848143, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848160, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848180, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848197, "dur": 73, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848271, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848273, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848291, "dur": 15, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848309, "dur": 15, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848326, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848343, "dur": 15, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848361, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848378, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848395, "dur": 16, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848413, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848436, "dur": 14, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848453, "dur": 20, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848476, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848490, "dur": 81, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848573, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848593, "dur": 32, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848628, "dur": 13, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848645, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848662, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848679, "dur": 14, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848696, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848712, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848730, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848757, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848774, "dur": 83, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848859, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848878, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848903, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848925, "dur": 19, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848947, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848970, "dur": 17, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827848990, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849011, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849028, "dur": 16, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849047, "dur": 15, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849064, "dur": 46, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849114, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849144, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849146, "dur": 19, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849167, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849189, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849191, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849213, "dur": 51, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849269, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849289, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849291, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849312, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849335, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849337, "dur": 50, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849391, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849418, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849419, "dur": 23, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849445, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849447, "dur": 28, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849478, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849480, "dur": 29, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849510, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849512, "dur": 22, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849536, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849538, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849557, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849617, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849638, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849640, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849663, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849666, "dur": 41, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849711, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849732, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849733, "dur": 19, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849754, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849756, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849782, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849784, "dur": 19, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849805, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849807, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849828, "dur": 16, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849846, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849848, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849904, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849928, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849930, "dur": 22, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849954, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849956, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849981, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827849983, "dur": 16, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850001, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850020, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850022, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850043, "dur": 18, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850064, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850084, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850086, "dur": 46, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850136, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850156, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850157, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850178, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850180, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850207, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850228, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850230, "dur": 17, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850250, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850252, "dur": 22, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850275, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850277, "dur": 16, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850297, "dur": 14, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850314, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850366, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850395, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850397, "dur": 24, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850424, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850426, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850448, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850450, "dur": 16, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850468, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850469, "dur": 26, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850499, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850520, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850522, "dur": 13, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850538, "dur": 26, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850567, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850589, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850591, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850643, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850668, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850670, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850693, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850694, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850717, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850719, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850740, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850764, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850765, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850786, "dur": 14, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850803, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850825, "dur": 41, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850869, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850891, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850892, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850913, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850915, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850939, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850941, "dur": 20, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850963, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850965, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850984, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827850986, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851008, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851029, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851079, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851101, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851103, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851129, "dur": 15, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851146, "dur": 16, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851165, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851185, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851204, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851222, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851241, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851258, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851289, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851310, "dur": 56, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851370, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851394, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851395, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851418, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851442, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851465, "dur": 16, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851484, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851502, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851521, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851537, "dur": 13, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851552, "dur": 46, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851601, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851623, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851625, "dur": 21, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851648, "dur": 16, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851668, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851691, "dur": 14, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851707, "dur": 15, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851725, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851743, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851762, "dur": 12, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851777, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851824, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851841, "dur": 96, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851942, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851965, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827851967, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852004, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852030, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852054, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852056, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852083, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852105, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852106, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852129, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852131, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852191, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852214, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852217, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852239, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852241, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852269, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852272, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852297, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852299, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852322, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852324, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852348, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852351, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852366, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852369, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852391, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852393, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852414, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852418, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852444, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852446, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852468, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852469, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852485, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852486, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852507, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852527, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852531, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852553, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852627, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852644, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852646, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852666, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852669, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852692, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852694, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852718, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852721, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852744, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852746, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852769, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852770, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852795, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852797, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852819, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852821, "dur": 18, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852842, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852844, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852865, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852867, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852890, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852892, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852919, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852922, "dur": 16, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852939, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852942, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852962, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852964, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852988, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827852991, "dur": 24, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853017, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853019, "dur": 20, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853042, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853044, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853070, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853072, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853096, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853099, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853123, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853126, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853149, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853152, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853178, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853181, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853205, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853207, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853231, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853233, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853254, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853256, "dur": 18, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853277, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853279, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853301, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853304, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853324, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853326, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853348, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853350, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853370, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853372, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853390, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853393, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853414, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853418, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853441, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853443, "dur": 23, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853468, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853470, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853495, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853497, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853519, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853522, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853543, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853545, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853573, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853575, "dur": 16, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853592, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853594, "dur": 16, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853613, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853615, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853637, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853639, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853666, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853669, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853688, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853710, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853731, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853754, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853755, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853776, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853778, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853800, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853802, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853827, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853830, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853851, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853871, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853873, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853899, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853917, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853919, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853941, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853960, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853962, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853980, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827853983, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854006, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854009, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854032, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854033, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854051, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854053, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854074, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854075, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854097, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854099, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854119, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854139, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854141, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854160, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854161, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854180, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854181, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854203, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854252, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827854275, "dur": 2789, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857070, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857095, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857097, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857119, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857121, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857242, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827857268, "dur": 2917, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860190, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860224, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860257, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860301, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860303, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860486, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827860501, "dur": 1310, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827861816, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827861835, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827861880, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827861896, "dur": 994, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827862895, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827862912, "dur": 1066, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827863983, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864014, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864077, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864160, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864180, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864182, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864199, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864231, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864247, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864279, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864299, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864301, "dur": 99, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864403, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864417, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864438, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864455, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864599, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864617, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864629, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864658, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864673, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864691, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864709, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864722, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864749, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864764, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864786, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864798, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864833, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864850, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864862, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864877, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864879, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864898, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827864914, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865011, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865026, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865075, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865093, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865117, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865131, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865149, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865251, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865267, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865302, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865320, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865370, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865390, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865402, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865419, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865431, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865449, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865499, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865518, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865520, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865539, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865592, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865611, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865631, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865655, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865676, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865690, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865700, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865735, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865754, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865791, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865804, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865840, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865861, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865970, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827865989, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866004, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866028, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866041, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866098, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866111, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866113, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866138, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866152, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866200, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866216, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866233, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866246, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866292, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866312, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866438, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866455, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866472, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866504, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866521, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866557, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866574, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866675, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866688, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866708, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866731, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866771, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866784, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866823, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866836, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866864, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866883, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866904, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866906, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866921, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866937, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866979, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827866998, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867011, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867014, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867028, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867044, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867058, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867107, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867123, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867125, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867144, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867157, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867176, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867191, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867228, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867245, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867293, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867296, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867317, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867351, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867367, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867422, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867438, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867453, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867512, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867526, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867552, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867567, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867569, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867639, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867659, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867672, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867676, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867707, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867725, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867747, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867759, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867827, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867842, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867854, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867963, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827867979, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868010, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868025, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868217, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868238, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868331, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868351, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868353, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868382, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868402, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868489, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868503, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868532, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868552, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868688, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868708, "dur": 176, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868888, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868910, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827868931, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869006, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869021, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869097, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869117, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869139, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869170, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869189, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869192, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869324, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869348, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869398, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869413, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869415, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869521, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869538, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869579, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869599, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869603, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869709, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869734, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869757, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869806, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869831, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869852, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869874, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869893, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827869987, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870010, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870029, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870031, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870053, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870261, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870280, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870281, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870306, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870309, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870391, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870411, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870414, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870491, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870493, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870518, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870593, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870616, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870678, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870697, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870716, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870943, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827870969, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871005, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871039, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871085, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871102, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871123, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871125, "dur": 652, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871781, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871803, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871830, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871918, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827871938, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827872292, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827872333, "dur": 50552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827922896, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827922903, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827922939, "dur": 34, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827922975, "dur": 3486, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926467, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926491, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926493, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926541, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926557, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926576, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827926579, "dur": 1190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927774, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927792, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927877, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927900, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927902, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927925, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827927999, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827928024, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827928026, "dur": 1084, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929113, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929116, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929131, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929227, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929241, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929260, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929263, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929283, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929303, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929305, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929325, "dur": 576, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929906, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827929934, "dur": 612, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930550, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930573, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930575, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930631, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930647, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930687, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930705, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930882, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930903, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827930906, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827931160, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827931180, "dur": 795, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827931978, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932000, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932026, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932042, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932121, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932140, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932241, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932260, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932288, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932303, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932581, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827932605, "dur": 845, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933455, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933476, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933506, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933518, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933550, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933562, "dur": 226, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933792, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933817, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827933833, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934084, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934112, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934153, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934174, "dur": 743, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934920, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934942, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827934969, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935066, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935082, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935102, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935104, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935131, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935144, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935281, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935300, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935373, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935388, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935406, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935417, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935419, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935434, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935477, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935491, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935505, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935522, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935538, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935678, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935692, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935712, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935725, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935742, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935755, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935778, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935797, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935818, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935834, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935849, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935854, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935867, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935884, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935907, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935921, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935940, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935963, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935980, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827935995, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936011, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936028, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936042, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936073, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936089, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936103, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936152, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936169, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936185, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936199, "dur": 13, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936214, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936234, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936256, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936257, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936273, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936290, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936312, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936336, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936392, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936412, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936436, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936439, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936463, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936480, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936481, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936499, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936517, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936520, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936541, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936544, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936565, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936567, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936585, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936599, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936601, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936620, "dur": 14, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936635, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936637, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936658, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936660, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936684, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936686, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936711, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936730, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936732, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936755, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936758, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936779, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936781, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936799, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936821, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936836, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936856, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936879, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936905, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936977, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827936981, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937012, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937014, "dur": 264, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937282, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937298, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937317, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937320, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937389, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937414, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937415, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937442, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937468, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721827937470, "dur": 1083377, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829020858, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829020863, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829020888, "dur": 277, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829021167, "dur": 236453, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829257646, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829257657, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829257730, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829257736, "dur": 47773, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829305521, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829305526, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829305559, "dur": 28, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829305589, "dur": 6561, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829312163, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829312169, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829312199, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829312204, "dur": 1240, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829313448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829313451, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829313484, "dur": 30, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829313516, "dur": 287607, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601134, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601141, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601173, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601178, "dur": 635, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601820, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601839, "dur": 30, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829601871, "dur": 617, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829602493, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829602518, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753721829602524, "dur": 7683, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20752, "tid": 998, "ts": 1753721829610814, "dur": 2238, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 21474836480, "ts": 1753721827710950, "dur": 139257, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753721827850209, "dur": 32, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 998, "ts": 1753721829613055, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20752, "tid": 17179869184, "ts": 1753721827708591, "dur": 1901673, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753721827708737, "dur": 2174, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753721829610267, "dur": 79, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753721829610285, "dur": 21, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753721829610352, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20752, "tid": 998, "ts": 1753721829613066, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753721827796056, "dur": 1601, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753721827797669, "dur": 1093, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753721827798928, "dur": 81, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753721827799010, "dur": 431, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753721827800635, "dur": 303, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_DDB1499AED0A17A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753721827801778, "dur": 161, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753721827802865, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.KdTree.ref.dll_4D920020B3EACDAF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753721827803414, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.Management.ref.dll_404BC0C35449934F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753721827803875, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753721827804180, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_3152B6A9836FBF3B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753721827804486, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753721827805308, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753721827808748, "dur": 30726, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753721827799466, "dur": 51825, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753721827851306, "dur": 1750191, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753721829601498, "dur": 250, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753721829601942, "dur": 1350, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753721827799579, "dur": 51733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827851315, "dur": 5591, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827856906, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827857344, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827857847, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827858214, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827858621, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827859692, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827860101, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827860317, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827860595, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827861402, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827863496, "dur": 1222, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool2x4.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753721827862236, "dur": 2483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827864823, "dur": 492, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753721827865560, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753721827864719, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753721827865860, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827865919, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827866018, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866311, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866135, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866386, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866507, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866635, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866772, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753721827866912, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753721827867283, "dur": 2659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827869946, "dur": 60111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827930060, "dur": 1438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\com.vrchat.core.vpm-resolver.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753721827930058, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.vrchat.core.vpm-resolver.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753721827932935, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753721827934418, "dur": 1468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753721827936026, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827936232, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753721827936346, "dur": 1664984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827799612, "dur": 51716, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827851341, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827851460, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A5D79986F9CBC457.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827851513, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827851961, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827852101, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827852473, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827852664, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827853029, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753721827853229, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753721827855394, "dur": 866, "ph": "X", "name": "File", "args": {"detail": "Packages\\nadena.dev.modular-avatar\\Editor\\Inspector\\MaterialSwap\\MatSwapEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753721827853628, "dur": 2653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827856281, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827856813, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827857858, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827858744, "dur": 896, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-stdio-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753721827858462, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827860312, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827860585, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827861333, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827861697, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827862518, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827863260, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827863540, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827863738, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827863863, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827863955, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827864056, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827864170, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827864312, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827864773, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827864834, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827864961, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827865062, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827865137, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827865664, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827866619, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753721827866825, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827867372, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827867470, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827867989, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827868463, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827868849, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827869263, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827869739, "dur": 54656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827924396, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827925940, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827927329, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827930061, "dur": 1413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.core.vpm-resolver\\Editor\\Dependencies\\SemanticVersioning.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753721827928683, "dur": 2830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827931514, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753721827931703, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827933293, "dur": 1500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827935782, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753721827936097, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753721827934833, "dur": 1972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753721827936856, "dur": 1664626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827799605, "dur": 51714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827851424, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1753721827851398, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C24F50ECF6FDA5D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827851487, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827851564, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827851692, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827851953, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827852228, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753721827852316, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753721827853629, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827854376, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827854783, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827855245, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827855757, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827856553, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827856943, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827857409, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827857854, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827858346, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827859125, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.26\\Rider\\Editor\\RiderScriptEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753721827858960, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827859847, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827860400, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827861152, "dur": 1322, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Sequence\\TrimClip.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753721827860772, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827862947, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827863372, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827863514, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827863632, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827863754, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827864359, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827864447, "dur": 870, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827865317, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827865901, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753721827866008, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753721827867300, "dur": 410, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753721827867711, "dur": 2141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753721827865438, "dur": 4909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827870397, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827870462, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827870577, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827871176, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827871262, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753721827871369, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827871750, "dur": 52651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827924402, "dur": 1558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827925998, "dur": 1337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827927374, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827928720, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827930086, "dur": 1255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRLabs.AV3Manager.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827931342, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827931582, "dur": 1399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827933011, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827934448, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753721827936032, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753721827936293, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753721827934529, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753721827936901, "dur": 1664564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827799635, "dur": 51699, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827851359, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753721827851338, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827851577, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827851993, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753721827851991, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_82645536A6FD6826.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827853610, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827854274, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827855549, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Packages\\vrchat.blackstartx.gesture-manager\\Scripts\\Editor\\Modules\\Vrc3\\RadialCursor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753721827856145, "dur": 861, "ph": "X", "name": "File", "args": {"detail": "Packages\\vrchat.blackstartx.gesture-manager\\Scripts\\Editor\\Modules\\Vrc3\\Params\\Vrc3Param.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753721827854929, "dur": 2545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827857475, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827857922, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827858334, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827859119, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827860082, "dur": 799, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.formats.fbx@4.2.1\\Editor\\IExportData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753721827859751, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827860934, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827862051, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827862444, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827863124, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827863508, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827863655, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827864302, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827864472, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827864598, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827864696, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827865132, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753721827864904, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827865562, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827865753, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827865886, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827866010, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827866138, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827866283, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827866440, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753721827866567, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827867101, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827867640, "dur": 2201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827869846, "dur": 60208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827930055, "dur": 1337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827931437, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827932857, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827933258, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753721827932933, "dur": 1549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827934482, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753721827935351, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Embedded.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753721827934594, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753721827936448, "dur": 1664864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827799654, "dur": 51688, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827851347, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753721827851533, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827851991, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827853120, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827853303, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753721827853605, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827854248, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827854716, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827855564, "dur": 878, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.avatars\\Editor\\VRCSDK\\SDK3A\\Elements\\CreateObjectField\\VRCCreateObjectField.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753721827855152, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827856571, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827857536, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\UniTask\\Runtime\\Linq\\ToHashSet.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753721827856794, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827858338, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827859813, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827860041, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827860384, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827860672, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827861807, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827862175, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827863402, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827863510, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753721827863738, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753721827863690, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827864638, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827865195, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753721827865377, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753721827865450, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827866350, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\VRC.Dynamics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753721827865929, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827866566, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827867128, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753721827867015, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827867602, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827867677, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827868152, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827868584, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827869034, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753721827869163, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753721827869285, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827869745, "dur": 54652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827924398, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827925959, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827926027, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827927454, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827929232, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827930616, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.ShaderStripping.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827932043, "dur": 1429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827933473, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827934391, "dur": 628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753721827933542, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753721827935773, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827935876, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827936247, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753721827936866, "dur": 1664467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827799676, "dur": 51675, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827851400, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1753721827851356, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827851579, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827851850, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753721827851849, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_38B99B8F076E9E13.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827851905, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827853460, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753721827853555, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3390009870488217095.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753721827853642, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827854280, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827854741, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827855823, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827856575, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827856819, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827857293, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827857861, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827858211, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827858629, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827859083, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827859750, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827859983, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827860194, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827860948, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827861456, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827862150, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827862955, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827863380, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827863512, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827863738, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753721827863618, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827864879, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827865048, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827865108, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827865560, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753721827865302, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827865870, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827865964, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827866218, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827866323, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827866691, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753721827867296, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753721827866881, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827867947, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827868373, "dur": 1390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753721827868343, "dur": 1747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827870131, "dur": 59930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827930062, "dur": 1487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827931550, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827932040, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827933614, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753721827935075, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827935563, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827935776, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827936237, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753721827936459, "dur": 1664858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827799696, "dur": 51663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827851404, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1753721827851363, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D2E6889350BDFFF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827851571, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753721827851570, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_37BDC0AA90481C1D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827851927, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_32AAC65751B4F737.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827851980, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827853607, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827854458, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Packages\\nadena.dev.ndmf\\Editor\\PreviewSystem\\NDMFPreviewPrefs.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753721827854217, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827855489, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827856434, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827856724, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827857100, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827857646, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827857983, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827858455, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827858836, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827859257, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827859917, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827860164, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827860375, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827860650, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827861665, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Actions\\ActionContext.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753721827861259, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827862433, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827863386, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827863745, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827863866, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827863962, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827864153, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827864822, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753721827864539, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827865272, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827865660, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827866073, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753721827866911, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Editor\\VRCSDK\\Dependencies\\VRChat\\Validation\\Performance\\SDKPerformanceDisplay.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753721827866185, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827867162, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827867310, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827867841, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827868552, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827868977, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827869744, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827870165, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827870536, "dur": 53863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827924402, "dur": 1466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827925922, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/FbxBuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827927228, "dur": 1295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827928569, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827930050, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753721827930003, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827931482, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.param-introspection.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827932924, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721827933250, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.vrchat.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827934741, "dur": 1499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753721827936306, "dur": 1375058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753721829311366, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753721829311365, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753721829311591, "dur": 1320, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753721829312914, "dur": 288415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827799714, "dur": 51652, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827851410, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1753721827851370, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_DD36A0C7D3C71D14.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827851462, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827851580, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827851579, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_07CC1135D7CDEE66.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827851652, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827851868, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827852568, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827852671, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753721827853717, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827854847, "dur": 1347, "ph": "X", "name": "File", "args": {"detail": "Packages\\nadena.dev.modular-avatar\\Runtime\\ModularAvatarRenameVRChatCollisionTags.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753721827856238, "dur": 917, "ph": "X", "name": "File", "args": {"detail": "Packages\\nadena.dev.modular-avatar\\Runtime\\ModularAvatarParameters.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753721827854609, "dur": 3305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827857914, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827858372, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827858996, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827859668, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753721827859667, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753721827859832, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827859929, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827860444, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827861202, "dur": 1287, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Extensions\\AnimationTrackExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753721827861078, "dur": 2016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827863120, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827863511, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827863641, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827864243, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827864483, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827864919, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827864994, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827865112, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827865251, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827865416, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827865495, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827866404, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827866491, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753721827866605, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827867042, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827867216, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827867663, "dur": 2380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827870049, "dur": 54355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827924405, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827925908, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827927142, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753721827927982, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827927240, "dur": 1467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827928746, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827930050, "dur": 1425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827930006, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827933258, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827932965, "dur": 1552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827935006, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\VRCCore-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827935782, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827936032, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753721827934554, "dur": 2145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753721827936749, "dur": 1664741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827799739, "dur": 51635, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827851379, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_ED9976D1F71D37ED.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827851479, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827851561, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753721827851559, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5AA8A4432B23411B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827851642, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827851820, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827851979, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827852101, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827852387, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753721827852439, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827852621, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827853203, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753721827853749, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753721827855513, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827855674, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827855863, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827856113, "dur": 751, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827856945, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827857070, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827857176, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827857701, "dur": 442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827858226, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827858311, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827858450, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827858509, "dur": 1148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827859828, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827859891, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827860023, "dur": 535, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827860559, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\StencilMaterial.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827860794, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827860935, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827860999, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827852794, "dur": 8434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827861357, "dur": 873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827862231, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827863005, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\DropdownEditor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753721827862358, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827863505, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827863658, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827864160, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753721827863749, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827864450, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827864577, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827864689, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827864762, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827865330, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827865393, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827865497, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753721827865614, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827865996, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827866053, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827866180, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1753721827866771, "dur": 108, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827867147, "dur": 55182, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1753721827924392, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827925928, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827927300, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827927456, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827928781, "dur": 1333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827930148, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.ExampleCentral.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827931461, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827932911, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827934381, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753721827936230, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827936303, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753721827936909, "dur": 1664477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827799758, "dur": 51623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827851418, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1753721827851384, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CABBC8F7A32A378E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827851581, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1753721827851566, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E6235C248C569650.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827851634, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827851733, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827852001, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827852669, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753721827852967, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827853623, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827854390, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827854952, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827855831, "dur": 1110, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.base\\Editor\\VRCSDK\\Dependencies\\VRChat\\Public SDK API\\IVRCSdkBuilderApi.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753721827855484, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827857469, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\UniTask\\Runtime\\UnityAsyncExtensions.Jobs.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753721827857217, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827858490, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827859718, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827860656, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827861314, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827861632, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Playables\\PrefabControlPlayable.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753721827861594, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827862761, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827862984, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827863154, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827863510, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827863629, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827864120, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827864293, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827864890, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827865125, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827865253, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827865679, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827865913, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827866020, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827866120, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827866232, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827866364, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827866972, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827867714, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753721827867423, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827867950, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827868385, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827868780, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827869194, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753721827869331, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827869757, "dur": 54636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827924394, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827925930, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827928128, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753721827927354, "dur": 1353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827928752, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827930342, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.harmony-patches.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827931751, "dur": 1395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827933146, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827933272, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753721827934661, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827934996, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827935416, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.DOTween.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753721827935717, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827936239, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753721827936768, "dur": 1664571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827799785, "dur": 51602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827851426, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1753721827851390, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_CA7ACE1B9A63F8DC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827851567, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827851774, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827851990, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827853632, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827854377, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827854795, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827855539, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.base\\Integrations\\VRChat Example Central\\Editor\\Algolia.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753721827855318, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827856499, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827856844, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827857698, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753721827857561, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827858772, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827859215, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827859909, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827860128, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827860352, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827860559, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827861440, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827862083, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827863124, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827863516, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827863635, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827863752, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827864247, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827864712, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827865114, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827865353, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.Csg.ref.dll_1438A2D2654458F5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827865446, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827865571, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827865892, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753721827865693, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827866240, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827866373, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827866493, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827866638, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827866754, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753721827866857, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827867128, "dur": 610, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753721827867099, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827868145, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827868629, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827869033, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827869490, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827869753, "dur": 54638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827924398, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827925947, "dur": 1341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753721827927288, "dur": 9030, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753721827936339, "dur": 1664976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827799830, "dur": 51568, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827851399, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AF9F947519E8CC62.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827851472, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827851558, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827851998, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721827851997, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A49F289DC8D09E11.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827852090, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827852623, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827853601, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721827853735, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721827855289, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753721827853045, "dur": 3430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753721827856562, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827859055, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEditor.TestRunner\\UnityTestProtocol\\Message.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753721827856696, "dur": 2539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753721827859236, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827859648, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827859716, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827860036, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827860270, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827860495, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827861126, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827862023, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827862532, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827863224, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827863727, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827863883, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827863935, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753721827864388, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827864851, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827865125, "dur": 441, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721827865701, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721827864957, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753721827866151, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827866331, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827866461, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753721827867286, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753721827867670, "dur": 111, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721827868420, "dur": 1151872, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753721829022136, "dur": 231718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721829022134, "dur": 233105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SkinnedMeshTools.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753721829256654, "dur": 263, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753721829256992, "dur": 47982, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SkinnedMeshTools.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753721829311362, "dur": 289125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721829311361, "dur": 289129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753721829600526, "dur": 753, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753721829606962, "dur": 2241, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20752, "tid": 998, "ts": 1753721829613233, "dur": 1416, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20752, "tid": 998, "ts": 1753721829614693, "dur": 6954, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20752, "tid": 998, "ts": 1753721829610784, "dur": 10921, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}