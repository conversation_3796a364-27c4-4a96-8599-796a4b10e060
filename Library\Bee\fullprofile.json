{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20752, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20752, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20752, "tid": 2388, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20752, "tid": 2388, "ts": 1753725441991426, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20752, "tid": 2388, "ts": 1753725441991447, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20752, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20752, "tid": 1, "ts": 1753725441847126, "dur": 2442, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753725441849570, "dur": 16564, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753725441866136, "dur": 29356, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20752, "tid": 2388, "ts": 1753725441991452, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 20752, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441847093, "dur": 14299, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441861394, "dur": 129666, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441861403, "dur": 29, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441861436, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441861616, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441861632, "dur": 7, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441861640, "dur": 2363, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864007, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864049, "dur": 2, "ph": "X", "name": "ProcessMessages 1444", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864052, "dur": 21, "ph": "X", "name": "ReadAsync 1444", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864075, "dur": 21, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864100, "dur": 29, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864131, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864134, "dur": 26, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864163, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864165, "dur": 23, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864192, "dur": 20, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864215, "dur": 46, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864265, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864299, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864301, "dur": 26, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864329, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864331, "dur": 24, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864359, "dur": 19, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864381, "dur": 21, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864405, "dur": 21, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864429, "dur": 21, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864451, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864453, "dur": 20, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864477, "dur": 19, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864499, "dur": 14, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864515, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864544, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864546, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864583, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864585, "dur": 28, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864615, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864617, "dur": 26, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864645, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864647, "dur": 29, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864678, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864680, "dur": 17, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864700, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864720, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864737, "dur": 18, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864757, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864759, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864781, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864783, "dur": 21, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864807, "dur": 13, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864822, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864841, "dur": 18, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864862, "dur": 16, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864880, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864900, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864901, "dur": 26, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864930, "dur": 16, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864949, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864972, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864974, "dur": 17, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441864994, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865012, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865033, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865054, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865081, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865105, "dur": 17, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865126, "dur": 20, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865149, "dur": 17, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865168, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865170, "dur": 16, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865188, "dur": 15, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865206, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865224, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865225, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865245, "dur": 16, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865263, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865282, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865305, "dur": 17, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865325, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865344, "dur": 17, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865363, "dur": 16, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865382, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865400, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865419, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865440, "dur": 16, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865458, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865459, "dur": 18, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865479, "dur": 15, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865498, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865517, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865537, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865555, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865574, "dur": 37, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865614, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865635, "dur": 17, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865654, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865672, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865691, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865715, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865732, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865733, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865758, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865776, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865793, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865811, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865829, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865846, "dur": 14, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865864, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865879, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865897, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865917, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865934, "dur": 15, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865952, "dur": 16, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865970, "dur": 12, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441865985, "dur": 14, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866002, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866020, "dur": 16, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866038, "dur": 15, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866056, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866073, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866091, "dur": 24, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866117, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866133, "dur": 77, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866213, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866232, "dur": 18, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866254, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866271, "dur": 17, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866291, "dur": 14, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866309, "dur": 13, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866324, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866342, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866361, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866379, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866380, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866398, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866415, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866416, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866434, "dur": 14, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866451, "dur": 16, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866469, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866486, "dur": 20, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866508, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866526, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866543, "dur": 15, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866561, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866583, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866602, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866619, "dur": 15, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866637, "dur": 21, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866661, "dur": 15, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866679, "dur": 15, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866696, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866714, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866735, "dur": 14, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866751, "dur": 14, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866767, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866786, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866804, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866823, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866846, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866867, "dur": 14, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866883, "dur": 15, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866901, "dur": 18, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866921, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866939, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866959, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866960, "dur": 18, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866981, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441866999, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867014, "dur": 16, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867033, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867051, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867073, "dur": 17, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867092, "dur": 16, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867109, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867111, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867129, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867145, "dur": 17, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867164, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867166, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867190, "dur": 15, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867207, "dur": 19, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867228, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867246, "dur": 13, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867262, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867279, "dur": 17, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867298, "dur": 15, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867316, "dur": 17, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867336, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867354, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867372, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867390, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867416, "dur": 17, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867435, "dur": 12, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867449, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867468, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867487, "dur": 13, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867503, "dur": 17, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867521, "dur": 15, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867539, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867557, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867575, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867594, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867612, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867613, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867630, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867658, "dur": 21, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867683, "dur": 15, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867700, "dur": 16, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867720, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867737, "dur": 13, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867752, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867770, "dur": 16, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867788, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867806, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867823, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867825, "dur": 15, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867842, "dur": 13, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867858, "dur": 16, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867876, "dur": 15, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867894, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867913, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867929, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867947, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867964, "dur": 12, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867978, "dur": 17, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441867998, "dur": 15, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868015, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868032, "dur": 15, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868050, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868067, "dur": 12, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868082, "dur": 16, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868100, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868118, "dur": 18, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868139, "dur": 12, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868153, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868181, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868199, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868221, "dur": 12, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868235, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868256, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868274, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868292, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868311, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868329, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868331, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868346, "dur": 16, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868365, "dur": 16, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868384, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868403, "dur": 14, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868420, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868442, "dur": 13, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868457, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868476, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868498, "dur": 16, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868517, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868543, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868544, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868567, "dur": 19, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868590, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868613, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868615, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868637, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868660, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868661, "dur": 17, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868682, "dur": 18, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868703, "dur": 17, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868722, "dur": 13, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868737, "dur": 13, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868753, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868771, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868773, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868791, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868808, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868823, "dur": 16, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868842, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868866, "dur": 18, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868887, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868906, "dur": 14, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868923, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868940, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868956, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868976, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868993, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441868994, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869014, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869031, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869049, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869074, "dur": 16, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869092, "dur": 12, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869107, "dur": 14, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869123, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869142, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869161, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869178, "dur": 14, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869194, "dur": 15, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869212, "dur": 14, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869228, "dur": 16, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869246, "dur": 15, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869265, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869287, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869306, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869326, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869344, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869360, "dur": 19, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869381, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869399, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869415, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869417, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869434, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869452, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869472, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869489, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869505, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869522, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869540, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869563, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869586, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869605, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869620, "dur": 16, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869640, "dur": 14, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869656, "dur": 14, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869673, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869704, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869721, "dur": 15, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869739, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869760, "dur": 12, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869775, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869828, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869847, "dur": 14, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869864, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869881, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869898, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869899, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869918, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869936, "dur": 14, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869953, "dur": 14, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869970, "dur": 14, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441869986, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870028, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870046, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870062, "dur": 15, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870080, "dur": 10, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870092, "dur": 44, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870138, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870156, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870174, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870192, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870210, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870230, "dur": 15, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870248, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870265, "dur": 14, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870282, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870329, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870348, "dur": 14, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870365, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870385, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870402, "dur": 17, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870420, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870422, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870441, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870461, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870477, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870494, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870553, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870583, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870585, "dur": 61, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870648, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870650, "dur": 23, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870677, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870705, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870706, "dur": 76, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870787, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870816, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870818, "dur": 28, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870850, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870870, "dur": 63, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870938, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870971, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441870973, "dur": 26, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871002, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871004, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871022, "dur": 84, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871111, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871141, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871143, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871171, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871173, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871197, "dur": 76, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871277, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871304, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871306, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871331, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871333, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871355, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871357, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871418, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871445, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871472, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871491, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871547, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871574, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871597, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871599, "dur": 19, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871621, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871668, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871691, "dur": 11, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871704, "dur": 12, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871719, "dur": 14, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871736, "dur": 44, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871782, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871784, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871808, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871832, "dur": 41, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871875, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871896, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871898, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871919, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871921, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871938, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441871983, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872001, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872020, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872037, "dur": 49, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872089, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872112, "dur": 15, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872130, "dur": 16, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872148, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872149, "dur": 53, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872205, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872227, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872228, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872246, "dur": 14, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872263, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872316, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872335, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872356, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872376, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872392, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872441, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872459, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872477, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872499, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872517, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872535, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872554, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872571, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872572, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872589, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872609, "dur": 41, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872652, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872674, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872694, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872712, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872713, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872737, "dur": 17, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872758, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872786, "dur": 11, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872799, "dur": 13, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872815, "dur": 14, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872831, "dur": 41, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872874, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872893, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872913, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872931, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872951, "dur": 16, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872970, "dur": 11, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441872984, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873004, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873020, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873037, "dur": 44, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873084, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873103, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873123, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873125, "dur": 19, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873147, "dur": 17, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873166, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873167, "dur": 16, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873186, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873207, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873226, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873243, "dur": 15, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873259, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873261, "dur": 34, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873297, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873316, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873335, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873354, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873373, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873389, "dur": 18, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873409, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873429, "dur": 13, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873444, "dur": 15, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873462, "dur": 42, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873506, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873527, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873545, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873565, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873566, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873591, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873593, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873615, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873633, "dur": 16, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873650, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873652, "dur": 13, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873668, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873686, "dur": 85, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873773, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873796, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873798, "dur": 26, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873826, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873828, "dur": 20, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873851, "dur": 17, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873871, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873890, "dur": 14, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873907, "dur": 14, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873923, "dur": 13, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873938, "dur": 40, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873981, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441873998, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874017, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874034, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874036, "dur": 14, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874053, "dur": 32, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874087, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874105, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874124, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874141, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874168, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874221, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874244, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874263, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874264, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874283, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874284, "dur": 21, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874309, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874328, "dur": 17, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874348, "dur": 13, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874363, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874380, "dur": 40, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874423, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874441, "dur": 18, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874462, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874483, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874504, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874506, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874527, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874546, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874547, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874564, "dur": 15, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874582, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874627, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874647, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874666, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874683, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874684, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874702, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874725, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874743, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874766, "dur": 150, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874918, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874936, "dur": 43, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441874982, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875000, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875001, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875021, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875058, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875077, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875078, "dur": 15, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875096, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875113, "dur": 15, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875130, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875176, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875198, "dur": 13, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875214, "dur": 16, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875232, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875250, "dur": 16, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875269, "dur": 16, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875287, "dur": 15, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875304, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875322, "dur": 13, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875338, "dur": 14, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875356, "dur": 13, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875371, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875406, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875435, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875455, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875472, "dur": 39, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875513, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875534, "dur": 13, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875549, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875570, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875589, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875607, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875624, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875644, "dur": 13, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875660, "dur": 14, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875676, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875722, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875741, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875759, "dur": 14, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875776, "dur": 43, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875821, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875839, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875857, "dur": 13, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875872, "dur": 16, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875891, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875892, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875911, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875928, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875948, "dur": 13, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875963, "dur": 15, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441875980, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876027, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876045, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876062, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876082, "dur": 41, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876125, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876143, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876161, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876179, "dur": 55, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876236, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876254, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876273, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876290, "dur": 45, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876337, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876354, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876372, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876389, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876408, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876425, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876444, "dur": 16, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876463, "dur": 13, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876478, "dur": 14, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876494, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876495, "dur": 38, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876536, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876562, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876578, "dur": 18, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876599, "dur": 18, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876621, "dur": 44, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876668, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876691, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876710, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876712, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876735, "dur": 15, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876753, "dur": 16, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876772, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876793, "dur": 13, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876808, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876826, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876879, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876899, "dur": 13, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876914, "dur": 14, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876932, "dur": 19, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876952, "dur": 2, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876955, "dur": 19, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876977, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876978, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441876999, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877020, "dur": 15, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877038, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877057, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877108, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877130, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877148, "dur": 13, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877163, "dur": 12, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877177, "dur": 16, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877196, "dur": 11, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877209, "dur": 14, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877226, "dur": 14, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877242, "dur": 16, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877260, "dur": 15, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877278, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877295, "dur": 14, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877312, "dur": 55, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877370, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877395, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877398, "dur": 19, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877420, "dur": 14, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877437, "dur": 42, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877481, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877502, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877521, "dur": 15, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877540, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877561, "dur": 15, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877579, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877600, "dur": 19, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877621, "dur": 12, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877636, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877653, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877699, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877723, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877745, "dur": 13, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877760, "dur": 39, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877802, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877823, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877824, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877849, "dur": 18, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877871, "dur": 16, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877889, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877911, "dur": 15, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877929, "dur": 15, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877946, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441877964, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878011, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878030, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878048, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878069, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878086, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878105, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878123, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878140, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878157, "dur": 16, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878175, "dur": 40, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878218, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878240, "dur": 14, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878256, "dur": 17, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878276, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878277, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878296, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878314, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878333, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878353, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878354, "dur": 17, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878374, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878419, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878437, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878456, "dur": 16, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878473, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878475, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878497, "dur": 15, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878515, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878534, "dur": 11, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878546, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878564, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878581, "dur": 41, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878624, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878647, "dur": 15, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878665, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878682, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878683, "dur": 17, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878703, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878720, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878737, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878739, "dur": 14, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878756, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878773, "dur": 12, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878788, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878825, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878842, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878844, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878863, "dur": 13, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878879, "dur": 16, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878897, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878914, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878932, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878950, "dur": 13, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878966, "dur": 14, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441878983, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879032, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879052, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879070, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879086, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879087, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879110, "dur": 15, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879128, "dur": 15, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879145, "dur": 16, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879164, "dur": 14, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879181, "dur": 17, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879200, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879245, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879264, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879284, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879302, "dur": 16, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879320, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879337, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879338, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879360, "dur": 13, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879375, "dur": 16, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879394, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879441, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879461, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879478, "dur": 13, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879493, "dur": 16, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879512, "dur": 15, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879529, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879547, "dur": 16, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879565, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879585, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879602, "dur": 13, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879617, "dur": 35, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879654, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879672, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879690, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879709, "dur": 22, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879733, "dur": 14, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879751, "dur": 17, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879770, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879794, "dur": 13, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879811, "dur": 16, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879830, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879881, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879901, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879919, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879937, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879957, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879977, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441879994, "dur": 14, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880012, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880028, "dur": 13, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880045, "dur": 37, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880083, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880101, "dur": 16, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880119, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880138, "dur": 15, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880156, "dur": 15, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880174, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880194, "dur": 20, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880217, "dur": 13, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880233, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880251, "dur": 10, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880264, "dur": 45, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880311, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880333, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880351, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880353, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880371, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880391, "dur": 14, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880407, "dur": 15, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880425, "dur": 16, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880443, "dur": 14, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880460, "dur": 15, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880476, "dur": 13, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880492, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880533, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880555, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880573, "dur": 15, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880591, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880611, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880629, "dur": 16, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880648, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880664, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880680, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880682, "dur": 13, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880698, "dur": 45, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880745, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880763, "dur": 15, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880781, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880799, "dur": 16, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880818, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880836, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880857, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880875, "dur": 13, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880892, "dur": 15, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880910, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880962, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441880982, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881002, "dur": 15, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881018, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881020, "dur": 15, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881037, "dur": 15, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881055, "dur": 15, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881072, "dur": 16, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881090, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881092, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881109, "dur": 15, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881127, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881179, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881197, "dur": 16, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881217, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881233, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881235, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881254, "dur": 17, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881274, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881292, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881309, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881326, "dur": 14, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881342, "dur": 38, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881384, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881400, "dur": 70, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881474, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881500, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881502, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881518, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881537, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881539, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881560, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881564, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881586, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881608, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881610, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881628, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881629, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881647, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881649, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881674, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881676, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881695, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881713, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881715, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881735, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881739, "dur": 29, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881771, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881774, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881799, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881802, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881824, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881826, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881869, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881890, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881893, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881909, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881911, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881931, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881933, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881954, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441881956, "dur": 49, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882007, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882009, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882036, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882039, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882061, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882064, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882088, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882090, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882120, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882123, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882148, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882151, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882177, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882179, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882200, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882203, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882226, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882228, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882255, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882257, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882276, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882280, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882299, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882301, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882328, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882331, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882349, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882351, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882375, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882378, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882395, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882413, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882427, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882447, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882449, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882466, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882470, "dur": 14, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882487, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882488, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882503, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882505, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882523, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882526, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882541, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882543, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882569, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882593, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882596, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882623, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882625, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882651, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882653, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882677, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882681, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882706, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882709, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882733, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882735, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882760, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882763, "dur": 16, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882781, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882783, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882801, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882825, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882827, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882845, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882847, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882863, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882881, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882883, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882908, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882910, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882930, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882932, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882954, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882955, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882975, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882977, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882991, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441882992, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883018, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883020, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883042, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883044, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883064, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883066, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883087, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883109, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883111, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883129, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883148, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883150, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883173, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883195, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883197, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883217, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883219, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883242, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883244, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883261, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883288, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883309, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883311, "dur": 98, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883414, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883437, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883439, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441883454, "dur": 3068, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886528, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886553, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886570, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886589, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886591, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886610, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886872, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441886892, "dur": 1011, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441887909, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441887936, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441887939, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441887962, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888009, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888033, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888280, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888305, "dur": 130, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888439, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888462, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888463, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888478, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888497, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888512, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888574, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888585, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888600, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888603, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888616, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888753, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888769, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888899, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888911, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888924, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441888944, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889017, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889072, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889074, "dur": 102, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889181, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889198, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889220, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889236, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889309, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889322, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889336, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889353, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889367, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889389, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889401, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889425, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889440, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889495, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889511, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889587, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889605, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889625, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889683, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889699, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889714, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889734, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889747, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889763, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889779, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889795, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889809, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889825, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889858, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889870, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889901, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889922, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889936, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441889955, "dur": 126, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890084, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890101, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890104, "dur": 99, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890206, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890218, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890232, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890339, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890353, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890393, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890407, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890424, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890437, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890454, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890466, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890481, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890497, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890512, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890529, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890544, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890558, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890572, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890598, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890613, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890675, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890691, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890711, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890713, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890744, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890757, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890841, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890862, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890864, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890885, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890887, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890901, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890923, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890937, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441890999, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891018, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891036, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891050, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891084, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891097, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891126, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891143, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891144, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891205, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891223, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891254, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891270, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891289, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891317, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891332, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891350, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891429, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891443, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891658, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891670, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891684, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891736, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891748, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891829, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891845, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891859, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891879, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441891894, "dur": 159, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892056, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892072, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892165, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892180, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892374, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892398, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892413, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892441, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892458, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892517, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892530, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892533, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892558, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892573, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892758, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892777, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892860, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892878, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892880, "dur": 13, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441892896, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893004, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893020, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893067, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893079, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893090, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893104, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893121, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893123, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893139, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893167, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893182, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893201, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893215, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893348, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893363, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893400, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893425, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893550, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893571, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893590, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893592, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893617, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893619, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893642, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893656, "dur": 338, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441893999, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441894024, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441894278, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441894304, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441894550, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441894578, "dur": 42011, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441936596, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441936601, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441936623, "dur": 24, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441936648, "dur": 3633, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940290, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940315, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940317, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940342, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940358, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940418, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441940434, "dur": 698, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941136, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941151, "dur": 574, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941728, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941746, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941747, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941767, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941769, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941910, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941923, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441941987, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942000, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942050, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942064, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942110, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942124, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942158, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942174, "dur": 498, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942675, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441942692, "dur": 546, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943244, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943257, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943271, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943336, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943348, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943422, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943438, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943553, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943568, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943627, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943644, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943714, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441943728, "dur": 495, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944226, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944242, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944518, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944542, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944670, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944685, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944774, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944789, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944937, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441944951, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441945123, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441945141, "dur": 210, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441945354, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441945371, "dur": 424, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441945798, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441945812, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946029, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946043, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946062, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946078, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946092, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946152, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946166, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946476, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946491, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946550, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946564, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946609, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946633, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946635, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441946657, "dur": 648, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947310, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947325, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947536, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947548, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947604, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947622, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947637, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947650, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947666, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947712, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947734, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947748, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947764, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947779, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947797, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947814, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947834, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947858, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947872, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947890, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947908, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947929, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947947, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947963, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947980, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441947995, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948010, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948027, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948043, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948059, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948076, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948126, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948128, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948149, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948152, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948170, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948172, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948187, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948204, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948221, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948239, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948253, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948270, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948272, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948298, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948300, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948324, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948326, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948342, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948343, "dur": 11, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948357, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948370, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948387, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948408, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948410, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948428, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948429, "dur": 18, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948450, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948468, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948470, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948489, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948490, "dur": 24, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948517, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948520, "dur": 329, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948852, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948875, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948891, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948930, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441948952, "dur": 33516, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441982477, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441982481, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441982528, "dur": 19, "ph": "X", "name": "ReadAsync 7081", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441982551, "dur": 2779, "ph": "X", "name": "ProcessMessages 13845", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441985332, "dur": 436, "ph": "X", "name": "ReadAsync 13845", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441985772, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753725441985792, "dur": 5264, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20752, "tid": 2388, "ts": 1753725441991460, "dur": 2073, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 21474836480, "ts": 1753725441847051, "dur": 48448, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753725441895500, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753725441895501, "dur": 26, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 2388, "ts": 1753725441993536, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20752, "tid": 17179869184, "ts": 1753725441844378, "dur": 146717, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753725441844472, "dur": 2142, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753725441991099, "dur": 59, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753725441991114, "dur": 16, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753725441991159, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20752, "tid": 2388, "ts": 1753725441993553, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753725441862739, "dur": 1364, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441864111, "dur": 762, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441864988, "dur": 56, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753725441865045, "dur": 269, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441871983, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753725441865336, "dur": 17453, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441882804, "dur": 103058, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441985863, "dur": 117, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441985981, "dur": 117, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441987167, "dur": 861, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753725441865482, "dur": 17328, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441883202, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725441883201, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F97977208FF60E8C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441883478, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753725441883615, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753725441884046, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753725441884539, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753725441884689, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441885125, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441885325, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441885555, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441885773, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441885978, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441886177, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441886434, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441886642, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441887251, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441887458, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441887683, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441888056, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441888276, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441888410, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441888856, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441889320, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441889856, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441889975, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725441890434, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725441890899, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441891004, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441891124, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441891179, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725441891855, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441891966, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441892094, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441892242, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441892411, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753725441892678, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725441892748, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753725441892675, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725441893288, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753725441893710, "dur": 85, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441983817, "dur": 92, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753725441894425, "dur": 89682, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441865511, "dur": 17314, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441882828, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441883200, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725441883199, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_917E5BD489C19DA6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441883678, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753725441883870, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753725441884262, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753725441884416, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10650598698298418623.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753725441884684, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441885022, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441885237, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441885453, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441885690, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441885883, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441886150, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441886367, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441886582, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441886789, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441887300, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441887510, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441888045, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441888264, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441888469, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441889053, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441889324, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441889709, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441889867, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441889989, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441890094, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441890328, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441890451, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441890935, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441891202, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441891351, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441891803, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441891919, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441892088, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441892257, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441892498, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753725441893081, "dur": 1326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.core.vpm-resolver\\Editor\\Dependencies\\Serilog.Sinks.Unity3D.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725441892716, "dur": 1703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441894465, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441894991, "dur": 45084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441940076, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441942581, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753725441941728, "dur": 1754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441943511, "dur": 1567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441945113, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441946750, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753725441948394, "dur": 1023, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753725441949893, "dur": 35966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441865506, "dur": 17313, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441882960, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753725441882958, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_6CDB56D6AEFB4AFA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725441883184, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753725441883183, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A1A281210CE26D07.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725441883992, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753725441884469, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441884632, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5168971709575573186.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753725441884701, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441884832, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441885080, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441885283, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441885495, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441885699, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441885895, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441886089, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441886298, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441886495, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441886702, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441887211, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441887413, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441887610, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441887995, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441888196, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441888414, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441888724, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441889324, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441889700, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725441889842, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441890446, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441891215, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725441891331, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441891934, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753725441892073, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441892580, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441892834, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753725441892831, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1753725441893547, "dur": 107, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441893964, "dur": 44013, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1753725441940035, "dur": 1598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441941667, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441943070, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753725441943151, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441944635, "dur": 1399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441946068, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441947422, "dur": 1480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.vrchat.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441948936, "dur": 1288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753725441950256, "dur": 35600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441865531, "dur": 17300, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441882834, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725441883210, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725441883209, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8C5E2A3FA2C2B638.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725441883562, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753725441883702, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753725441883950, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753725441884103, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753725441884171, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753725441884267, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753725441884674, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441884827, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441885112, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441885313, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441885538, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441885733, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441885933, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441886130, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441886354, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441886554, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441886773, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441887270, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441887484, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441887695, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441888123, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\half3.gen.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753725441888123, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441888987, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441889315, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441889716, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725441889871, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441890468, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441890620, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441890753, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725441890902, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441891560, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441891812, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725441892581, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Editor\\EditorCore\\VertexOnFaceEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753725441891946, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441892784, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441893079, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753725441893236, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441893717, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441894284, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\Harmony\\0Harmony.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725441893781, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441894572, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441894989, "dur": 45046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441940037, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441941679, "dur": 1410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441943126, "dur": 1465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.ProBuilder.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753725441943125, "dur": 2906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441946070, "dur": 1453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441947553, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753725441949682, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753725441949828, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753725441949892, "dur": 35968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441865551, "dur": 17286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441882885, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1753725441882840, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441883186, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725441883184, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_955A791FD004EFEC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441883643, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753725441883905, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753725441884669, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441884818, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441885024, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441885255, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441885462, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441885887, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441886264, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441886427, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441886631, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441887129, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441887535, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441887945, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441888169, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441888425, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441888720, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441889317, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441889705, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441889849, "dur": 491, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441890812, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\PostProcessEffectSettings.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753725441890341, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441891059, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441891212, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441891347, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441891488, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441892017, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441892098, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441892244, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441892400, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441892526, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753725441892672, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441893243, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441893802, "dur": 487, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725441894399, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753725441893772, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441894943, "dur": 45109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441940054, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441941706, "dur": 1631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441943338, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753725441943559, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441945026, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441946525, "dur": 1453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.harmony-patches.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441948009, "dur": 1519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRLabs.AV3Manager.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753725441949891, "dur": 35967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441865578, "dur": 17265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441882846, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D2E6889350BDFFF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725441883155, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441883610, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725441884506, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725441884671, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725441885650, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725441886825, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753725441887704, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Shadow.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753725441887799, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753725441883719, "dur": 4164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441887942, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441888152, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441888630, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441888754, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441889322, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441889703, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725441889837, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441890463, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725441890581, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441891093, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441891495, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441892138, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725441892281, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753725441892482, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441893136, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441893595, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441893842, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441894260, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441894753, "dur": 2178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441896931, "dur": 3053, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441899984, "dur": 40055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441940042, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441941672, "dur": 1437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441943109, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441943387, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441944950, "dur": 1445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.ShaderStripping.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441946395, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441946522, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441948048, "dur": 1608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.ExampleCentral.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753725441949656, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753725441949902, "dur": 35952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441865604, "dur": 17244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441882851, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_DD36A0C7D3C71D14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441883218, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725441883217, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_5A104ACC7C39F2CD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441883755, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753725441884159, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753725441884684, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441884847, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441885111, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441885328, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441885566, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441885849, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441886151, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441886401, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441886632, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441887147, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441887442, "dur": 654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\ObjectExtension.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753725441888124, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\Graphics.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753725441887416, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441888958, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441889314, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441889707, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441889857, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441889992, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441890160, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441890706, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441891034, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725441890823, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441891791, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441891949, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441892086, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441892678, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725441893082, "dur": 669, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725441892674, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441893754, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441893808, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441894270, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441894400, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753725441894597, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441895031, "dur": 45025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441940057, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441941661, "dur": 867, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441942530, "dur": 1466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441943997, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441944074, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441945538, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441945623, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.param-introspection.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441947134, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753725441947193, "dur": 1480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441949470, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.DependencyInjection.Abstractions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725441949608, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Abstractions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753725441948704, "dur": 1539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753725441950278, "dur": 35591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441865624, "dur": 17231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441883096, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_14FD492B07BA3A39.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441883195, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441883194, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441883338, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441883413, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441883609, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441884411, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441884641, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441884801, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441885093, "dur": 401, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441885816, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753725441886829, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753725441883769, "dur": 3933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441887703, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441887971, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441888495, "dur": 446, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441888260, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441889330, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441889400, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441889698, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441889834, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441890284, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441890597, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441890732, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441891213, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441891306, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441891484, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441891609, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441891740, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441891997, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441892185, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441892319, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753725441892443, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441893008, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441893251, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441893702, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441894399, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Editor\\Effects\\LensDistortionEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753725441893955, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441894611, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441895003, "dur": 45044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441940049, "dur": 1587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441941675, "dur": 1408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441943136, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441944712, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441944822, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441946348, "dur": 1128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441947480, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441948950, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753725441949468, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-heap-l1-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441949777, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.DiaSymReader.Native.amd64.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753725441949005, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.vrchat.core.vpm-resolver.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753725441950334, "dur": 35529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441865648, "dur": 17213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441882891, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441883188, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_48F6D28C1C558B25.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441883284, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_48F6D28C1C558B25.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441883866, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753725441883987, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753725441884199, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753725441884675, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441884815, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441885016, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441885235, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441885426, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441885632, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441885853, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441886050, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441886261, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441886451, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441886664, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441887163, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441887364, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441887562, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441887984, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441888202, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441888417, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441889068, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441889345, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441889711, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441889861, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441890014, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441890334, "dur": 483, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441890150, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441891018, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441891089, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441891264, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753725441891396, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441891835, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441892375, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441892581, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441892429, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441892962, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441893077, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441893564, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441894143, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441894399, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441894585, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441894263, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441895042, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441895389, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441895668, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441895943, "dur": 44101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753725441940071, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441940045, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441941703, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441943128, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\nadena.dev.ndmf.runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441943127, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441944732, "dur": 1760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441946520, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441949215, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753725441947950, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753725441949904, "dur": 35970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441865671, "dur": 17233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441883199, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441883584, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753725441884678, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441884814, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441885046, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441885242, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441885452, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441885678, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441885875, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441886054, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441886302, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441886502, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441886771, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441887269, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441887483, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441887675, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441888030, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441888248, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441888464, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441888819, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441889313, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441889708, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441889847, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441889980, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725441889899, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441890700, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441890790, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441891343, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441891624, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441891738, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441892249, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441892407, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441892528, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441892741, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441893238, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441893646, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441893848, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441894277, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753725441894506, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441894997, "dur": 45040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441940038, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441941685, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441941747, "dur": 1592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441943340, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441944586, "dur": 1064, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725441943449, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441945914, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441947463, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753725441948966, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441949045, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441949526, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\nadena.dev.modular-avatar.harmony-patches.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725441949525, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/nadena.dev.modular-avatar.harmony-patches.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753725441949827, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/lyuma.av3emulator.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753725441949880, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753725441950264, "dur": 35608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441865688, "dur": 17193, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441883027, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_435CAD0D25883954.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725441883203, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441883675, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753725441884201, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753725441884354, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1930740807194350409.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753725441884418, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10012524876404317990.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753725441884652, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441884804, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441885018, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441885289, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441885511, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441885725, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441885931, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441886149, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441886392, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441886614, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441886825, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441887350, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441887573, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441888021, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441888251, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441888487, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441888930, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441889330, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441889726, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725441889862, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441890421, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441890853, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441890994, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725441891138, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725441891306, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725441891267, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441892067, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441892289, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753725441892420, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441892834, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725441892732, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441893457, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441893966, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441894526, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753725441894155, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441894747, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441895034, "dur": 45034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441940069, "dur": 1563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441941633, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441941820, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441943316, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441944825, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441946336, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441947873, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753725441949514, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441949888, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753725441950337, "dur": 35528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441865709, "dur": 17181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441883145, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753725441883144, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6B90C7C6C86CFDA0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725441883432, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_221627215FE73EEA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725441883543, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753725441884101, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753725441884382, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16575931144855026204.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753725441884568, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5467569028037604947.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753725441884693, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441885261, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441885491, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441885708, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441885982, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441886184, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441886749, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441887256, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441887456, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441887658, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441888041, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441888254, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441888487, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441888921, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441889318, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441889703, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725441889841, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441890318, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725441890460, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441891087, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441891349, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725441891486, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753725441891588, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441891752, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441892229, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441892749, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753725441892604, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441893119, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441893279, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441893733, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441893803, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441894265, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441894792, "dur": 5194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441899987, "dur": 40054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441940042, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441941701, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/FbxBuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441943155, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441944562, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441944663, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441946173, "dur": 1536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441947710, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441948015, "dur": 1658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753725441949826, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753725441949883, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753725441950281, "dur": 35587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753725441990803, "dur": 1373, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20752, "tid": 2388, "ts": 1753725441993612, "dur": 294, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20752, "tid": 2388, "ts": 1753725441993942, "dur": 4556, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20752, "tid": 2388, "ts": 1753725441991436, "dur": 7094, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}