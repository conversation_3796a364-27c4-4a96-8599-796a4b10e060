{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20752, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20752, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20752, "tid": 689, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20752, "tid": 689, "ts": 1753719440462326, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20752, "tid": 689, "ts": 1753719440462345, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20752, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20752, "tid": 1, "ts": **********767045, "dur": 1421, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": **********768468, "dur": 16782, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": **********785252, "dur": 28027, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20752, "tid": 689, "ts": 1753719440462350, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 20752, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 34359738368, "ts": **********767021, "dur": 16054, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783076, "dur": 2678859, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783087, "dur": 24, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783113, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783115, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783295, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783321, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********783328, "dur": 2194, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785527, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785564, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785566, "dur": 32, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785604, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785629, "dur": 21, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785653, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785656, "dur": 22, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785680, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785682, "dur": 30, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785715, "dur": 19, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785736, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785756, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785775, "dur": 16, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785793, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785810, "dur": 30, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785843, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785871, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785872, "dur": 24, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785900, "dur": 17, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785919, "dur": 17, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785939, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785961, "dur": 18, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********785982, "dur": 23, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786007, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786009, "dur": 20, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786032, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786050, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786069, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786089, "dur": 14, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786106, "dur": 26, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786134, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786155, "dur": 16, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786172, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786175, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786194, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786196, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786218, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786240, "dur": 16, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786259, "dur": 16, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786278, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786298, "dur": 17, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786318, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786337, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786361, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786366, "dur": 29, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786397, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786398, "dur": 17, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786418, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786438, "dur": 16, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786457, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786477, "dur": 15, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786495, "dur": 25, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786523, "dur": 22, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786547, "dur": 17, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786567, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786588, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786612, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786635, "dur": 16, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786660, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786680, "dur": 19, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786702, "dur": 17, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786721, "dur": 16, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786740, "dur": 16, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786759, "dur": 16, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786778, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786797, "dur": 16, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786816, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786836, "dur": 14, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786852, "dur": 19, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786873, "dur": 15, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786891, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786909, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786929, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786948, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786950, "dur": 18, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786970, "dur": 15, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********786988, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787009, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787011, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787029, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787048, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787066, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787083, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787111, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787129, "dur": 16, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787148, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787166, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787186, "dur": 15, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787204, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787222, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787244, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787262, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787281, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787300, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787319, "dur": 11, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787332, "dur": 14, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787349, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787367, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787387, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787404, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787423, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787440, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787463, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787481, "dur": 14, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787498, "dur": 17, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787518, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787536, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787561, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787581, "dur": 14, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787598, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787625, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787641, "dur": 55, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787699, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787716, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787719, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787738, "dur": 15, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787755, "dur": 15, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787773, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787791, "dur": 14, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787807, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787825, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787845, "dur": 15, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787863, "dur": 15, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787881, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787899, "dur": 16, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787917, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787934, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787958, "dur": 25, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********787986, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788006, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788035, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788036, "dur": 19, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788059, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788077, "dur": 17, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788097, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788115, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788134, "dur": 31, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788167, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788187, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788205, "dur": 15, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788223, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788242, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788260, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788279, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788297, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788315, "dur": 12, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788330, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788356, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788374, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788392, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788411, "dur": 15, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788428, "dur": 15, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788445, "dur": 16, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788464, "dur": 14, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788480, "dur": 15, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788498, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788527, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788546, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788564, "dur": 16, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788583, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788601, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788623, "dur": 56, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788681, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788683, "dur": 16, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788701, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788718, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788734, "dur": 14, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788751, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788768, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788791, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788793, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788819, "dur": 17, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788839, "dur": 13, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788855, "dur": 15, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788873, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788893, "dur": 15, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788910, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788927, "dur": 16, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788945, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788965, "dur": 13, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788981, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********788998, "dur": 16, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789016, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789036, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789054, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789074, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789091, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789111, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789128, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789147, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789169, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789187, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789204, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789221, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789241, "dur": 16, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789260, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789278, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789298, "dur": 15, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789316, "dur": 13, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789331, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789347, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789365, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789387, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789405, "dur": 15, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789422, "dur": 15, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789439, "dur": 13, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789455, "dur": 21, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789477, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789478, "dur": 15, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789496, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789515, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789543, "dur": 14, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789559, "dur": 15, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789576, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789577, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789595, "dur": 16, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789614, "dur": 17, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789634, "dur": 15, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789651, "dur": 13, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789667, "dur": 16, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789685, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789703, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789721, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789740, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789758, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789775, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789796, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789816, "dur": 15, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789834, "dur": 28, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789865, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789890, "dur": 15, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789906, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789908, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789925, "dur": 17, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789945, "dur": 15, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789962, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789964, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********789991, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790012, "dur": 1, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790013, "dur": 20, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790036, "dur": 13, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790051, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790070, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790089, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790114, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790132, "dur": 17, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790151, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790168, "dur": 2, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790171, "dur": 15, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790187, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790204, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790228, "dur": 15, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790245, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790264, "dur": 14, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790280, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790295, "dur": 15, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790313, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790333, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790349, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790350, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790368, "dur": 16, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790386, "dur": 20, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790409, "dur": 12, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790425, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790442, "dur": 16, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790461, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790481, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790500, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790515, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790533, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790551, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790569, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790589, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790607, "dur": 17, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790627, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790643, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790661, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790683, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790702, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790720, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790738, "dur": 44, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790785, "dur": 16, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790804, "dur": 25, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790831, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790832, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790856, "dur": 14, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790873, "dur": 18, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790893, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790913, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790930, "dur": 17, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790949, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790974, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790976, "dur": 15, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********790994, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791014, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791034, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791052, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791068, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791090, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791111, "dur": 15, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791129, "dur": 14, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791145, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791163, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791181, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791199, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791216, "dur": 15, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791233, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791252, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791275, "dur": 16, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791294, "dur": 14, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791310, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791328, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791345, "dur": 14, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791362, "dur": 12, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791377, "dur": 26, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791406, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791424, "dur": 16, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791443, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791463, "dur": 14, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791479, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791550, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791579, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791581, "dur": 28, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791611, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791613, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791641, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791643, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791670, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791689, "dur": 17, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791709, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791727, "dur": 17, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791747, "dur": 14, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791764, "dur": 61, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791828, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791856, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791878, "dur": 1, "ph": "X", "name": "ProcessMessages 111", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791880, "dur": 24, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791907, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791909, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791927, "dur": 54, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********791985, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792010, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792012, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792038, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792040, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792065, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792066, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792091, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792093, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792118, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792137, "dur": 18, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792157, "dur": 13, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792173, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792213, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792234, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792255, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792274, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792291, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792310, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792329, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792330, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792349, "dur": 16, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792368, "dur": 14, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792384, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792423, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792443, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792463, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792466, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792485, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792522, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792543, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792562, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792580, "dur": 40, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792624, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792647, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792665, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792683, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792726, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792746, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792765, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792783, "dur": 42, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792828, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792849, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792869, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792887, "dur": 41, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********792931, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793196, "dur": 40, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793238, "dur": 3, "ph": "X", "name": "ProcessMessages 2776", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793243, "dur": 24, "ph": "X", "name": "ReadAsync 2776", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793270, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793272, "dur": 29, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793303, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793305, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793329, "dur": 61, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793395, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793427, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793429, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793458, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793460, "dur": 22, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793485, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793487, "dur": 50, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793541, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793561, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793562, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793581, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793600, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793602, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793679, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793712, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793714, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793738, "dur": 71, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793814, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793848, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793850, "dur": 16, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793869, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793933, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793959, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********793979, "dur": 17, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794000, "dur": 44, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794046, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794071, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794095, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794115, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794135, "dur": 19, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794157, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794183, "dur": 13, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794199, "dur": 14, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794215, "dur": 15, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794233, "dur": 38, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794274, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794295, "dur": 16, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794313, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794331, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794333, "dur": 131, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794468, "dur": 24, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794494, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794507, "dur": 36, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794545, "dur": 1, "ph": "X", "name": "ProcessMessages 1807", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794547, "dur": 19, "ph": "X", "name": "ReadAsync 1807", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794568, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794569, "dur": 17, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794589, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794609, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794627, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794644, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794693, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794718, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794736, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794763, "dur": 15, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794782, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794801, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794819, "dur": 15, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794836, "dur": 13, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794852, "dur": 16, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794871, "dur": 12, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794885, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794926, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794946, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794973, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********794994, "dur": 15, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795013, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795032, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795047, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795068, "dur": 13, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795084, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795104, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795125, "dur": 17, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795145, "dur": 13, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795161, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795200, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795224, "dur": 19, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795246, "dur": 16, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795264, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795287, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795306, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795324, "dur": 18, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795345, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795365, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795387, "dur": 83, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795474, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795497, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795517, "dur": 17, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795537, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795555, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795556, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795577, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795595, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795614, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795630, "dur": 40, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795673, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795693, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795711, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795730, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795748, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795768, "dur": 17, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795786, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795789, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795806, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795824, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795871, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795894, "dur": 22, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795918, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795920, "dur": 22, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795944, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795945, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795965, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********795988, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796007, "dur": 14, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796023, "dur": 15, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796041, "dur": 54, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796098, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796118, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796143, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796162, "dur": 24, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796188, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796190, "dur": 20, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796212, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796214, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796232, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796234, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796262, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796309, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796332, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796351, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796369, "dur": 15, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796387, "dur": 14, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796404, "dur": 17, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796424, "dur": 14, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796440, "dur": 159, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796602, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796624, "dur": 68, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796695, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796713, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796731, "dur": 17, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796750, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796769, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796788, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796806, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796824, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796843, "dur": 14, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796859, "dur": 14, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796876, "dur": 43, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796923, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796947, "dur": 28, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796977, "dur": 19, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********796999, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797021, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797024, "dur": 18, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797045, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797065, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797085, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797103, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797120, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797138, "dur": 43, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797183, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797202, "dur": 17, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797222, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797241, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797282, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797304, "dur": 14, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797320, "dur": 17, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797341, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797360, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797380, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797402, "dur": 25, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797430, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797432, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797454, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797503, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797523, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797542, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797560, "dur": 51, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797613, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797638, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797656, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797673, "dur": 15, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797690, "dur": 17, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797709, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797729, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797748, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797769, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797787, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797803, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797805, "dur": 16, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797826, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797852, "dur": 45, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797900, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797928, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797930, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********797952, "dur": 46, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798001, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798031, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798032, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798054, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798073, "dur": 64, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798142, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798167, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798169, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798193, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798194, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798214, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798261, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798284, "dur": 13, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798300, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798318, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798337, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798357, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798377, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798397, "dur": 14, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798414, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798431, "dur": 46, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798482, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798504, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798522, "dur": 16, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798540, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798586, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798611, "dur": 25, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798638, "dur": 16, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798657, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798674, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798698, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798717, "dur": 13, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798733, "dur": 45, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798780, "dur": 15, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798798, "dur": 17, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798819, "dur": 20, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798842, "dur": 13, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798857, "dur": 16, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798876, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798898, "dur": 15, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798916, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798945, "dur": 17, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798964, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********798983, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799035, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799058, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799085, "dur": 16, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799104, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799124, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799126, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799147, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799163, "dur": 30, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799195, "dur": 15, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799213, "dur": 17, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799233, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799289, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799315, "dur": 19, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799336, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799353, "dur": 37, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799392, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799415, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799432, "dur": 15, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799450, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799469, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799490, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799507, "dur": 17, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799527, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799544, "dur": 14, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799561, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799607, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799627, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799647, "dur": 15, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799663, "dur": 54, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799721, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799744, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799762, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799781, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799782, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799801, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799823, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799841, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799858, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799878, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799894, "dur": 41, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799939, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799961, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********799980, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800000, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800019, "dur": 17, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800039, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800058, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800074, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800077, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800094, "dur": 17, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800113, "dur": 47, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800163, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800183, "dur": 13, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800199, "dur": 16, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800218, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800238, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800257, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800281, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800283, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800310, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800327, "dur": 14, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800344, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800362, "dur": 46, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800411, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800432, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800454, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800473, "dur": 14, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800490, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800511, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800528, "dur": 13, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800544, "dur": 15, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800562, "dur": 15, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800579, "dur": 26, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800608, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800631, "dur": 53, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800687, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800706, "dur": 16, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800724, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800745, "dur": 18, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800766, "dur": 17, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800785, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800805, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800823, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800841, "dur": 13, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800856, "dur": 40, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800899, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800925, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800942, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800944, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800965, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800985, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********800987, "dur": 19, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801008, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801029, "dur": 15, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801047, "dur": 17, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801066, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801114, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801138, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801140, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801159, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801177, "dur": 15, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801195, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801215, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801232, "dur": 37, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801274, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801298, "dur": 14, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801315, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801334, "dur": 42, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801379, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801399, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801421, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801423, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801443, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801463, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801464, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801484, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801503, "dur": 14, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801519, "dur": 15, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801537, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801587, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801615, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801634, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801653, "dur": 17, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801674, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801691, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801692, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801712, "dur": 14, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801729, "dur": 18, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801749, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801799, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801819, "dur": 15, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801837, "dur": 20, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801859, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801877, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801894, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801914, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801932, "dur": 12, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801948, "dur": 18, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********801969, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802014, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802035, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802055, "dur": 16, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802073, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802092, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802110, "dur": 15, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802127, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802128, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802145, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802162, "dur": 13, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802178, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802218, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802236, "dur": 16, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802255, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802273, "dur": 16, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802290, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802292, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802311, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802333, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802336, "dur": 15, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802353, "dur": 14, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802369, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802370, "dur": 15, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802389, "dur": 13, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802404, "dur": 40, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802447, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802468, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802486, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802504, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802524, "dur": 14, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802540, "dur": 16, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802558, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802577, "dur": 12, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802592, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802609, "dur": 14, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802625, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802670, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802693, "dur": 16, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802712, "dur": 19, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802733, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802752, "dur": 19, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802773, "dur": 15, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802790, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802806, "dur": 15, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802824, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802875, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802894, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802913, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802930, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802932, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802950, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802978, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********802979, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803005, "dur": 15, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803023, "dur": 14, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803038, "dur": 14, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803055, "dur": 42, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803099, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803119, "dur": 14, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803135, "dur": 11, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803148, "dur": 13, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803164, "dur": 16, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803182, "dur": 15, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803199, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803202, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803221, "dur": 16, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803239, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803255, "dur": 15, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803274, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803322, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803343, "dur": 19, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803365, "dur": 16, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803384, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803402, "dur": 17, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803421, "dur": 15, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803439, "dur": 19, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803461, "dur": 12, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803476, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803494, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803538, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803557, "dur": 75, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803636, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803659, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803661, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803682, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803705, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803708, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803733, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803735, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803758, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803781, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803783, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803806, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803808, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803829, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803831, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803858, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803862, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803880, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803881, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803902, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803904, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803926, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803928, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803950, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803953, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803974, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********803976, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804000, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804002, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804023, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804073, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804089, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804090, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804109, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804111, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804135, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804137, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804156, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804158, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804178, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804180, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804201, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804225, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804227, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804247, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804248, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804270, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804295, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804297, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804318, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804320, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804336, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804337, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804359, "dur": 13, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804372, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804374, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804395, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804397, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804421, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804424, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804443, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804462, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804464, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804480, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804499, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804520, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804522, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804542, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804544, "dur": 20, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804567, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804569, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804590, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804611, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804613, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804634, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804636, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804659, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804662, "dur": 29, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804693, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804695, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804716, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804720, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804739, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804740, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804761, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804763, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804783, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804785, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804807, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804810, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804832, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804835, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804858, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804861, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804883, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804905, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804908, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804928, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804930, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804952, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804956, "dur": 21, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804979, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********804982, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805001, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805032, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805034, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805055, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805057, "dur": 16, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805075, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805078, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805100, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805102, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805116, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805135, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805157, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805159, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805181, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805183, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805203, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805205, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805229, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805232, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805255, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805257, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805279, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805281, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805302, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805304, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805327, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805329, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805354, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805356, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805375, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805377, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805398, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805400, "dur": 13, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805416, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805434, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805456, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805458, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805475, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805477, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805493, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805512, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805514, "dur": 14, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805530, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805547, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805548, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805562, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805581, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805602, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805699, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805722, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805725, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805741, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805743, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805762, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805780, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805782, "dur": 13, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805798, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805816, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805834, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805837, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805857, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805859, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805878, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805879, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805894, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********805911, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********806002, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********806022, "dur": 1540, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********807565, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********807568, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********807586, "dur": 1153, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808743, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808767, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808769, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808787, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808808, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808831, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808888, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********808904, "dur": 924, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********809833, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********809862, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********809884, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********809969, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********809991, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********809993, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810340, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810360, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810482, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810498, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810520, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810523, "dur": 12, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810537, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810606, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810632, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810652, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810671, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810738, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810753, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810767, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810811, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810824, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810861, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810872, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810928, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********810944, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811097, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811114, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811141, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811159, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811173, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811270, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811287, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811289, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811307, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811308, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811326, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811415, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811432, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811447, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811467, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811495, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811509, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811523, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811565, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811581, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811599, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811615, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811664, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811720, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811852, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811864, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811878, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811897, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811963, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811970, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811985, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********811999, "dur": 13, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812016, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812050, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812065, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812081, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812099, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812115, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812170, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812184, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812213, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812227, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812257, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812276, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812308, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812324, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812338, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812359, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812377, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812445, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812462, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812464, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812481, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812584, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812594, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812607, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812702, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812714, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812728, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812749, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812767, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812769, "dur": 114, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812885, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812904, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812918, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812933, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********812945, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813033, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813051, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813074, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813094, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813096, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813125, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813140, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813210, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813232, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813248, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813315, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813327, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813345, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813359, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813382, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813400, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813420, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813436, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813475, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813493, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813521, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813536, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813571, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813588, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813602, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813621, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813648, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813651, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813664, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813728, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813748, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813769, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813783, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813845, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813865, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813914, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813930, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813956, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********813974, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814032, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814048, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814051, "dur": 43, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814096, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814113, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814225, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814240, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814258, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814276, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814439, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814459, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814492, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814503, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814551, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814569, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814713, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814725, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814812, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814825, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814846, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********814859, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815053, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815074, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815169, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815189, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815257, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815271, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815370, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815388, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815407, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815470, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815481, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815495, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815511, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815527, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815540, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815555, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815570, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815585, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815624, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815642, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815718, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815741, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815798, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815818, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815820, "dur": 93, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815916, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815941, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815943, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815966, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********815988, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********816007, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********816009, "dur": 429, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********816443, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********816468, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********816813, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********816834, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********817181, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********817196, "dur": 38833, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********856041, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********856046, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********856070, "dur": 29, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********856101, "dur": 3494, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********859601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********859604, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********859626, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********859628, "dur": 952, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860586, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860603, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860700, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860714, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860718, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860751, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860769, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860975, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********860992, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861044, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861060, "dur": 428, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861491, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861502, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861552, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861564, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861752, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********861767, "dur": 514, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862284, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862302, "dur": 187, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862492, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862507, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862562, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862578, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862580, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862610, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862623, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862650, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********862671, "dur": 553, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863227, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863242, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863354, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863366, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863369, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863450, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********863466, "dur": 594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864063, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864074, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864091, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864104, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864123, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864277, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864298, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864319, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864333, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864461, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864477, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864757, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********864770, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865025, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865037, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865299, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865316, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865626, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865646, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865659, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865854, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865877, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865894, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865946, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********865961, "dur": 607, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********866571, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********866589, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********866818, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********866839, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********866868, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********866888, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867136, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867156, "dur": 348, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867508, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867528, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867592, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867610, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********867612, "dur": 485, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868100, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868116, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868318, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868337, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868363, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868382, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868422, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868437, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868460, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868479, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868497, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868513, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868534, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868553, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868570, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868588, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868603, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868619, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868636, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868657, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868672, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868688, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868706, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868719, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868736, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868752, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868770, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868790, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868808, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868824, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868841, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868860, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868861, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868879, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868897, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868914, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868935, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868937, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868955, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868972, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868992, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********868994, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869011, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869029, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869046, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869062, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869064, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869077, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869078, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869095, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869097, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869111, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869113, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869130, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869146, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869163, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869184, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869186, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869202, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869204, "dur": 14, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869221, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869234, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869300, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869325, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869363, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869384, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869386, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869403, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869490, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869510, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869534, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869559, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869582, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869584, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869924, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869953, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********869978, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********870011, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": **********870030, "dur": 826557, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438696596, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438696600, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438696634, "dur": 201, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438696836, "dur": 211233, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438908078, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438908082, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438908106, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719438908109, "dur": 1328981, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440237101, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440237105, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440237149, "dur": 20, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440237170, "dur": 5201, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440242377, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440242403, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440242406, "dur": 1443, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440243855, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440243871, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440243898, "dur": 211198, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455109, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455114, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455142, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455147, "dur": 509, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455661, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455666, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455702, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440455736, "dur": 504, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440456247, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 34359738368, "ts": 1753719440456266, "dur": 5664, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20752, "tid": 689, "ts": 1753719440462358, "dur": 2254, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 30064771072, "ts": **********766987, "dur": 46297, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 30064771072, "ts": **********813286, "dur": 21, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 689, "ts": 1753719440464615, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20752, "tid": 25769803776, "ts": **********764732, "dur": 2697240, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": **********764819, "dur": 1686, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753719440461975, "dur": 66, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753719440461988, "dur": 24, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20752, "tid": 25769803776, "ts": 1753719440462043, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20752, "tid": 689, "ts": 1753719440464624, "dur": 16, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": **********784065, "dur": 1144, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********785217, "dur": 758, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********786083, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": **********786141, "dur": 322, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********786492, "dur": 18043, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********804550, "dur": 2652143, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753719440456694, "dur": 281, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753719440457108, "dur": 83, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753719440457227, "dur": 1029, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": **********786537, "dur": 18019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********804988, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********805397, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": **********805477, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********805607, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********805854, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": **********805987, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********806207, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********806324, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********806593, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********806840, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********807084, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********807272, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********807468, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********807665, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********807855, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********808042, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********808279, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********808479, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********808686, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********809171, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********809369, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********809594, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********809791, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********809998, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********810195, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********810408, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********810677, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********810850, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********811368, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********811466, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********811646, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********812489, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********812639, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********813338, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": **********813160, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********813923, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********814217, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 1, "ts": **********814023, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********815028, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": **********814950, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********815703, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********816401, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********816460, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********816910, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********817424, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********817795, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********818161, "dur": 42483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********860964, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 1, "ts": **********860645, "dur": 1785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********863508, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": **********862480, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********864080, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********864216, "dur": 1469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********865685, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********866624, "dur": 504, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.HttpListener.dll"}}, {"pid": 12345, "tid": 1, "ts": **********867711, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.12\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 1, "ts": **********865761, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********867777, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********869990, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.12\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 1, "ts": **********868497, "dur": 1557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.ExampleCentral.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********870136, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********870211, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********871015, "dur": 2585691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********786562, "dur": 18011, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********804576, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********804710, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********804984, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********804982, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_917E5BD489C19DA6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********805444, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********805625, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********805854, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.rsp2"}}, {"pid": 12345, "tid": 2, "ts": **********806219, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********806425, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********806536, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********806854, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********807065, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********807257, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********807472, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********807682, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********807886, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********808179, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 2, "ts": **********808086, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********808803, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********809290, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********809489, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********809688, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********809912, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********810111, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********810310, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********810549, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********810849, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********811372, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********811510, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********811815, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********812305, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********812441, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********813578, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@2.1.6\\Scripts\\Runtime\\TMP_TextInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": **********813076, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********813743, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********813882, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********814068, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********814211, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********814325, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********814416, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********814639, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********815089, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********815541, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********816489, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********816945, "dur": 42082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********860618, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\SDKBase-Legacy.dll"}}, {"pid": 12345, "tid": 2, "ts": **********859028, "dur": 1992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********861021, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********861566, "dur": 1511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********863077, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********863556, "dur": 2154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": **********863274, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********867717, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": **********866841, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********869509, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": **********869932, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 2, "ts": **********870094, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 2, "ts": **********868584, "dur": 1743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********870371, "dur": 2586315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********786556, "dur": 18010, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********804706, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": **********804705, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_07CC1135D7CDEE66.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********804985, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********805295, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********806506, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": **********806695, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 3, "ts": **********805455, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********808573, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********808767, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********809240, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********809426, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********810028, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********810539, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********810848, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********811372, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********811497, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********811639, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********811741, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********811851, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********812318, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********812600, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********812709, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********812868, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********812981, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********813554, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********813750, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********813892, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********814080, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********814231, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********814346, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********814468, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********814584, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********815037, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********815767, "dur": 747, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 3, "ts": **********815485, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********816646, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********816702, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********816990, "dur": 42049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********859041, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********861201, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": **********860571, "dur": 1811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********862383, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********862544, "dur": 1763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********864344, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********865999, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********867707, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.12\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 3, "ts": **********866291, "dur": 1483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********867841, "dur": 434, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": **********869541, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 3, "ts": **********869893, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\Harmony\\0Harmony.dll"}}, {"pid": 12345, "tid": 3, "ts": **********867806, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********870096, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.ProBuilder.Stl.pdb"}}, {"pid": 12345, "tid": 3, "ts": **********870095, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Stl.pdb"}}, {"pid": 12345, "tid": 3, "ts": **********870193, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********870544, "dur": 2586167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********786582, "dur": 17997, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********804583, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********804930, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********805413, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********806042, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********806585, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********806846, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********807083, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********807284, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********807479, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********807699, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********808255, "dur": 851, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 4, "ts": **********807912, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********809204, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********809401, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********809617, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********809809, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********810034, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********810681, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********810859, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********811489, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********811815, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********812271, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********812406, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********812567, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********812871, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********812973, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********813080, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********813202, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********813673, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********814405, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XR.LegacyInputHelpers.ref.dll_FFA26A0728B3CF82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********814603, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********814730, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********815376, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********815544, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********816036, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********816564, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********816948, "dur": 42080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********859030, "dur": 1530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********860597, "dur": 1411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********862037, "dur": 1500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********863538, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********863604, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********865129, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********865291, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********866682, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********867712, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Http.Json.dll"}}, {"pid": 12345, "tid": 4, "ts": **********866867, "dur": 1490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.vrchat.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********868357, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********869892, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 4, "ts": **********870095, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.avatars\\Runtime\\VRCSDK\\Plugins\\VRCSDK3A-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": **********868591, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********870353, "dur": 2586335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********786600, "dur": 17986, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********804623, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": **********804589, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********804757, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********804937, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_78C161B79C1A5AEC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********805006, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 5, "ts": **********805005, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8C5E2A3FA2C2B638.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********805329, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********806040, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********806561, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********806838, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********807086, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********807290, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********807501, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********807704, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********807896, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********808087, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********808449, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********808809, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.autodesk.fbx@4.2.1\\Runtime\\Scripts\\FbxObject.cs"}}, {"pid": 12345, "tid": 5, "ts": **********808698, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********810142, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********810600, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********810836, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********811370, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********811767, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 5, "ts": **********811492, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********812505, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********812657, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********813081, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********813300, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********813444, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********813591, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********813694, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********813752, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********813935, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********814059, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********814225, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********814348, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********814466, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********814596, "dur": 441, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 5, "ts": **********814588, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********815723, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": **********815429, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********816508, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": **********816049, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********816614, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********816955, "dur": 42075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********859033, "dur": 1526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********860603, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********862043, "dur": 1523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********863601, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********865084, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.param-introspection.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********866583, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********867716, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 5, "ts": **********866926, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********869509, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": **********869704, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 5, "ts": **********869992, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": **********870142, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 5, "ts": **********868490, "dur": 1953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********870511, "dur": 2586188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********786621, "dur": 17974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********804980, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": **********804979, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********805175, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********805740, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 6, "ts": **********806572, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 6, "ts": **********807065, "dur": 663, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 6, "ts": **********808410, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 6, "ts": **********808820, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 6, "ts": **********805301, "dur": 4388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********809747, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********810438, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEditor.TestRunner\\Api\\RunState.cs"}}, {"pid": 12345, "tid": 6, "ts": **********809877, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********810858, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********810951, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********811361, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********811466, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********811976, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********812309, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********812413, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********812588, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********813143, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********813348, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********813461, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********814216, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 6, "ts": **********813899, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": **********814588, "dur": 105, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********815059, "dur": 41958, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": **********859025, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********860586, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********861963, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********863465, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********863642, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********865078, "dur": 1401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********866480, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********867712, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": **********866637, "dur": 1443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********868123, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********870200, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********870935, "dur": 2585774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********786637, "dur": 17966, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********804985, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********805404, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********805624, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": **********806037, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": **********806509, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14297382335031916063.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********806562, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********806888, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********807137, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********807330, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********807560, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********807763, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********808114, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********808338, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********808716, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Editor\\EditorCore\\ProBuilderToolManager.cs"}}, {"pid": 12345, "tid": 7, "ts": **********808552, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********809389, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********809602, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********809803, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********810256, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********810416, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********810542, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********810838, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********811488, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********811642, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********812074, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********812493, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 7, "ts": **********812280, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********812801, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********813081, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********813201, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********813322, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********813766, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 7, "ts": **********813441, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********814263, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********814402, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********814511, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********814604, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********815009, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********815223, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********815644, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********815834, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********816284, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********816507, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********816986, "dur": 42038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********859027, "dur": 1526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********860601, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********862050, "dur": 1474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********863559, "dur": 1453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********865055, "dur": 1530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********867712, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 7, "ts": **********866617, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.harmony-patches.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********868142, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRLabs.AV3Manager.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********870085, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********870213, "dur": 2372996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753719440243212, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753719440243211, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753719440243367, "dur": 1477, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753719440244848, "dur": 211852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********786655, "dur": 17956, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********804830, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": **********804829, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_07D67DBD8968BBA3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********804961, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********805246, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********806047, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": **********806431, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15611487661579288874.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********806587, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********806771, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14776904114713646495.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********806871, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********807200, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********807395, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********807591, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********807831, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********808031, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********808296, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********808448, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********808653, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********809138, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********809331, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********809548, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********809773, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********810213, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********810415, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********810593, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********810846, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********811374, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********811494, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********811627, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********811755, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********812095, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********812568, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********813178, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********813452, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********813576, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********814331, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********814416, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********814568, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********814720, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********815214, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********815708, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********815811, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********816252, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********816361, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********816484, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********816916, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********817879, "dur": 41153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********859035, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********860550, "dur": 1125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********861686, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********863551, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********866689, "dur": 1078, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": **********867842, "dur": 968, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 8, "ts": **********865454, "dur": 3553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********869008, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********869704, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Core.dll"}}, {"pid": 12345, "tid": 8, "ts": **********869992, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.StaticFiles.dll"}}, {"pid": 12345, "tid": 8, "ts": **********870143, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": **********869085, "dur": 1788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.vrchat.core.vpm-resolver.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********870926, "dur": 2585757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********786684, "dur": 17946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********804988, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********805529, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********805675, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********805966, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": **********806139, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********806362, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": **********806575, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********806712, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11721167980350881331.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********806860, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********807056, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********807257, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********807462, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********807673, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********807976, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********808203, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********808398, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********808617, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********809244, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********809444, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********809650, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********809848, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********810050, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********810264, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********810522, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********810837, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********811364, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********811503, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********812149, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********812272, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********812696, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********812859, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": **********812842, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********813622, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********813694, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********814120, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********814533, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********815029, "dur": 704, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": **********814719, "dur": 1201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********815921, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********816366, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 9, "ts": **********816159, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********816950, "dur": 43102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********860053, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********861740, "dur": 1503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********863554, "dur": 524, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 9, "ts": **********863278, "dur": 1998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********865699, "dur": 2074, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Editor\\VRCSDK\\Plugins\\VRC.SDK3.Dynamics.PhysBone.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": **********867843, "dur": 1676, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-util-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": **********869541, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-utility-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": **********869932, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 9, "ts": **********865313, "dur": 5642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********871007, "dur": 2585696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********786706, "dur": 17934, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********804982, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********805342, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********805545, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********805829, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********805969, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.rsp2"}}, {"pid": 12345, "tid": 10, "ts": **********806293, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********806581, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********806850, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********807064, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********807261, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********807463, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********807673, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********807863, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********808072, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********808273, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********808470, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********808677, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********809144, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********809335, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********809547, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********809825, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********810036, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********810215, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********810522, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********810853, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********811364, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********811622, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********812289, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********812443, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": **********813341, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\TrackGui\\TimelineTrackErrorGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": **********812425, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********813758, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********814070, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********814228, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********814701, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********814840, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********814902, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********815018, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********815767, "dur": 750, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": **********815483, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********816708, "dur": 69, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********816831, "dur": 880733, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753719438699280, "dur": 207574, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753719438699279, "dur": 208679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SkinnedMeshTools.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753719438908891, "dur": 118, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753719438909041, "dur": 1329084, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SkinnedMeshTools.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753719440243208, "dur": 212810, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753719440243207, "dur": 212813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753719440456041, "dur": 606, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 11, "ts": **********786725, "dur": 17931, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********804817, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 11, "ts": **********804815, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_041909FA9A88E487.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********804925, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 11, "ts": **********804993, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": **********804924, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_147A3C7BC0733851.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********805965, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.rsp2"}}, {"pid": 12345, "tid": 11, "ts": **********806562, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********806852, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********807068, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********807269, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********807476, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********807672, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********807880, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********808150, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********808362, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********808562, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********808762, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********809240, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********809583, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********809821, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********810219, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********810339, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********810591, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********810844, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********811368, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********811770, "dur": 678, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 11, "ts": **********812493, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": **********812579, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 11, "ts": **********811499, "dur": 1557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********813098, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********813206, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********813668, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********814076, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********814759, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********815251, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********815653, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********815797, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********816246, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********816508, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": **********816390, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********816988, "dur": 44088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********861077, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********862739, "dur": 1668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********864443, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.ShaderStripping.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********866016, "dur": 1429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********867446, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********867855, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********869526, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********869932, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.XR.Oculus.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": **********869932, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Oculus.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": **********870187, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********870373, "dur": 2586312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********786752, "dur": 17974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********804814, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********804986, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": **********804985, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F97977208FF60E8C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********805269, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.12\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 12, "ts": **********805268, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_221627215FE73EEA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********805400, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********805814, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********806132, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.rsp2"}}, {"pid": 12345, "tid": 12, "ts": **********806486, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********806549, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********806785, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********806987, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********807192, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********807389, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********807615, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********808114, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********808323, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********808538, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********808728, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********809207, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********809403, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********809602, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********809860, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********810077, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********810280, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********810525, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********810842, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********811456, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********811729, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********812314, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********812859, "dur": 911, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 12, "ts": **********812483, "dur": 1338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********813821, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********813879, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********813982, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********814315, "dur": 3561, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********817876, "dur": 41158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********859037, "dur": 1515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********860594, "dur": 1360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/FbxBuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********861983, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********863509, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 12, "ts": **********863483, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********864973, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********865699, "dur": 938, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.ApiExplorer.dll"}}, {"pid": 12345, "tid": 12, "ts": **********865111, "dur": 2304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********867416, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********867712, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 12, "ts": **********867558, "dur": 1607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********869166, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********869697, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********869765, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********869838, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********870188, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********870375, "dur": 2586306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753719440460854, "dur": 1726, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20752, "tid": 689, "ts": 1753719440464678, "dur": 15, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20752, "tid": 689, "ts": 1753719440464720, "dur": 4921, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20752, "tid": 689, "ts": 1753719440462336, "dur": 7331, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}