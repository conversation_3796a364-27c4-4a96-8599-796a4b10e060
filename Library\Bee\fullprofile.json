{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20752, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20752, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20752, "tid": 868, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20752, "tid": 868, "ts": 1753720589766849, "dur": 643, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20752, "tid": 868, "ts": 1753720589770772, "dur": 592, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20752, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20752, "tid": 1, "ts": 1753720588801546, "dur": 4926, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753720588806474, "dur": 23501, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753720588829984, "dur": 39818, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20752, "tid": 868, "ts": 1753720589771367, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 20752, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588799898, "dur": 10135, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588810035, "dur": 950048, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588810994, "dur": 2009, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588813008, "dur": 1427, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814437, "dur": 137, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814577, "dur": 10, "ph": "X", "name": "ProcessMessages 20517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814588, "dur": 24, "ph": "X", "name": "ReadAsync 20517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814614, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814615, "dur": 16, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814634, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814657, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814676, "dur": 51, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814730, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814760, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814762, "dur": 22, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814789, "dur": 17, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814810, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814830, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814848, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814864, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814867, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814887, "dur": 20, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814909, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814929, "dur": 16, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814947, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814966, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588814983, "dur": 18, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815004, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815024, "dur": 13, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815040, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815057, "dur": 16, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815076, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815097, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815117, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815136, "dur": 13, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815152, "dur": 14, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815168, "dur": 15, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815185, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815203, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815221, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815244, "dur": 14, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815260, "dur": 15, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815278, "dur": 16, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815296, "dur": 19, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815318, "dur": 15, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815335, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815359, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815379, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815397, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815413, "dur": 16, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815432, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815450, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815452, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815472, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815490, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815513, "dur": 14, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815529, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815546, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815548, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815566, "dur": 15, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815583, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815601, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815619, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815638, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815653, "dur": 15, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815672, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815693, "dur": 13, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815709, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815731, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815750, "dur": 13, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815766, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815785, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815807, "dur": 15, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815824, "dur": 14, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815841, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815860, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815877, "dur": 16, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815896, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815917, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815936, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815952, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815954, "dur": 17, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815975, "dur": 13, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588815991, "dur": 16, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816011, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816031, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816053, "dur": 462, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816517, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816519, "dur": 62, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816584, "dur": 4, "ph": "X", "name": "ProcessMessages 8337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816589, "dur": 182, "ph": "X", "name": "ReadAsync 8337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816774, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816775, "dur": 42, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816820, "dur": 2, "ph": "X", "name": "ProcessMessages 2848", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816824, "dur": 41, "ph": "X", "name": "ReadAsync 2848", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816867, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816869, "dur": 18, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816888, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816890, "dur": 20, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816914, "dur": 19, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816937, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816956, "dur": 15, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816974, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588816994, "dur": 19, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817015, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817018, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817035, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817054, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817074, "dur": 15, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817091, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817108, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817128, "dur": 14, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817143, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817145, "dur": 15, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817163, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817182, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817201, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817220, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817235, "dur": 17, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817255, "dur": 15, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817273, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817294, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817315, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817335, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817354, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817374, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817392, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817416, "dur": 16, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817435, "dur": 15, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817453, "dur": 15, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817471, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817487, "dur": 15, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817505, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817507, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817529, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817548, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817567, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817569, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817588, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817607, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817608, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817627, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817629, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817646, "dur": 16, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817665, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817686, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817704, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817706, "dur": 14, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817722, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817745, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817762, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817784, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817805, "dur": 16, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817823, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817843, "dur": 3, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817846, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817864, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817866, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817885, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817905, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817927, "dur": 12, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817941, "dur": 12, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817956, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817976, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588817999, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818020, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818040, "dur": 16, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818059, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818074, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818077, "dur": 11, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818090, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818109, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818129, "dur": 15, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818146, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818164, "dur": 15, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818181, "dur": 13, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818198, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818229, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818247, "dur": 17, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818268, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818288, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818290, "dur": 57, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818349, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818370, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818387, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818407, "dur": 16, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818427, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818445, "dur": 16, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818464, "dur": 13, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818480, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818499, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818545, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818571, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818593, "dur": 13, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818610, "dur": 40, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818653, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818676, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818696, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818716, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818717, "dur": 17, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818737, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818754, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818756, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818776, "dur": 14, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818793, "dur": 15, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818810, "dur": 45, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818861, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818887, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818889, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818912, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818914, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818935, "dur": 18, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818955, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818956, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818976, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588818997, "dur": 13, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819013, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819031, "dur": 54, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819091, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819125, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819128, "dur": 28, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819158, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819160, "dur": 73, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819237, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819269, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819272, "dur": 29, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819304, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819305, "dur": 53, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819363, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819396, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819398, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819423, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819426, "dur": 23, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819452, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819454, "dur": 78, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819535, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819565, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819587, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819588, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819608, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819677, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819710, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819714, "dur": 30, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819747, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819749, "dur": 41, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819793, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819818, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819820, "dur": 26, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819849, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819851, "dur": 21, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819875, "dur": 59, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819940, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819959, "dur": 16, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819978, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588819998, "dur": 14, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820015, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820062, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820085, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820104, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820124, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820171, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820193, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820194, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820218, "dur": 15, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820235, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820273, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820295, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820313, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820331, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820377, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820398, "dur": 19, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820420, "dur": 12, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820433, "dur": 181, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820618, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820652, "dur": 1, "ph": "X", "name": "ProcessMessages 2001", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820654, "dur": 17, "ph": "X", "name": "ReadAsync 2001", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820676, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820704, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820706, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820730, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820732, "dur": 15, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820750, "dur": 35, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820787, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820789, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820814, "dur": 20, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820836, "dur": 15, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820855, "dur": 24, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820880, "dur": 3, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820884, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820906, "dur": 15, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820924, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820941, "dur": 14, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588820957, "dur": 45, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821005, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821008, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821034, "dur": 17, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821053, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821054, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821075, "dur": 18, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821095, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821128, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821150, "dur": 13, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821166, "dur": 12, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821180, "dur": 49, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821232, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821257, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821276, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821277, "dur": 18, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821299, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821321, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821344, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821364, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821382, "dur": 15, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821399, "dur": 42, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821443, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821466, "dur": 28, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821499, "dur": 19, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821521, "dur": 20, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821545, "dur": 19, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821567, "dur": 12, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821580, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821600, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821618, "dur": 43, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821665, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821689, "dur": 19, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821709, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821711, "dur": 18, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821732, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821751, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821769, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821794, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821823, "dur": 2, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821826, "dur": 11, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821839, "dur": 47, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821889, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821912, "dur": 15, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821930, "dur": 17, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821950, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821971, "dur": 20, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821993, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588821995, "dur": 16, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822012, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822014, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822032, "dur": 13, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822049, "dur": 80, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822132, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822137, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822160, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822180, "dur": 17, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822200, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822221, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822240, "dur": 11, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822252, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822269, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822287, "dur": 43, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822333, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822355, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822374, "dur": 20, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822397, "dur": 15, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822414, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822447, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822468, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822487, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822505, "dur": 14, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822521, "dur": 14, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822538, "dur": 43, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822584, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822607, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822625, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822644, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822664, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822684, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822704, "dur": 13, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822720, "dur": 15, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822739, "dur": 44, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822786, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822805, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822832, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822856, "dur": 21, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822880, "dur": 28, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822914, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822933, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822935, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822953, "dur": 15, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588822971, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823018, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823040, "dur": 16, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823059, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823078, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823098, "dur": 14, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823114, "dur": 16, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823132, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823151, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823171, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823173, "dur": 140, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823316, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823335, "dur": 42, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823380, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823398, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823401, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823420, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823422, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823445, "dur": 16, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823462, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823464, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823484, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823503, "dur": 12, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823518, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823536, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823581, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823601, "dur": 13, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823618, "dur": 15, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823635, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823654, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823672, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823675, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823695, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823696, "dur": 16, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823716, "dur": 15, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823735, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823753, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823771, "dur": 15, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823787, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823789, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823845, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823866, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823886, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823903, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823951, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823970, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823988, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588823990, "dur": 15, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824008, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824031, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824050, "dur": 17, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824071, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824091, "dur": 14, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824108, "dur": 15, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824127, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824175, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824177, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824198, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824200, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824220, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824237, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824284, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824305, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824307, "dur": 16, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824325, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824345, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824348, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824370, "dur": 16, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824388, "dur": 18, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824409, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824428, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824445, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824463, "dur": 43, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824509, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824529, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824550, "dur": 15, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824568, "dur": 35, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824606, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824626, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824644, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824646, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824665, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824730, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824749, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824768, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824787, "dur": 46, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824837, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824860, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824880, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824903, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824905, "dur": 17, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824925, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824945, "dur": 16, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824963, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588824982, "dur": 15, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825001, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825042, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825063, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825079, "dur": 19, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825101, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825122, "dur": 14, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825138, "dur": 41, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825182, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825203, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825223, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825243, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825262, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825282, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825298, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825301, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825320, "dur": 14, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825337, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825382, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825405, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825424, "dur": 13, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825440, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825460, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825462, "dur": 16, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825481, "dur": 15, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825499, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825518, "dur": 13, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825534, "dur": 16, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825553, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825603, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825623, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825626, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825644, "dur": 17, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825664, "dur": 18, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825684, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825685, "dur": 17, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825706, "dur": 14, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825723, "dur": 15, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825741, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825761, "dur": 13, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825777, "dur": 14, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825794, "dur": 139, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825937, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825977, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588825978, "dur": 25, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826006, "dur": 45, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826054, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826081, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826083, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826104, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826128, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826130, "dur": 21, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826154, "dur": 16, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826173, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826191, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826209, "dur": 13, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826225, "dur": 43, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826271, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826293, "dur": 16, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826312, "dur": 15, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826328, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826330, "dur": 39, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826371, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826392, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826411, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826412, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826438, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826458, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826481, "dur": 15, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826499, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826517, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826533, "dur": 15, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826551, "dur": 42, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826595, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826616, "dur": 16, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826634, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826650, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826666, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826687, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826705, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826725, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826742, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826795, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826814, "dur": 15, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826832, "dur": 17, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826851, "dur": 22, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826875, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826876, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826898, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826912, "dur": 13, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826930, "dur": 10, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826941, "dur": 13, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588826957, "dur": 47, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827008, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827030, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827031, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827057, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827077, "dur": 18, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827096, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827098, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827113, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827115, "dur": 13, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827131, "dur": 16, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827150, "dur": 15, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827168, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827215, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827234, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827254, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827279, "dur": 17, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827298, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827317, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827336, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827353, "dur": 13, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827368, "dur": 39, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827409, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827431, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827447, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827449, "dur": 15, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827466, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827487, "dur": 16, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827506, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827533, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827536, "dur": 15, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827553, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827556, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827603, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827621, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827623, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827643, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827661, "dur": 15, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827679, "dur": 15, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827696, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827698, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827716, "dur": 15, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827734, "dur": 14, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827750, "dur": 14, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827766, "dur": 41, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827810, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827833, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827835, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827862, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827864, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827889, "dur": 16, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827907, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827927, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827950, "dur": 13, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827965, "dur": 16, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588827984, "dur": 14, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828001, "dur": 42, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828046, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828068, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828071, "dur": 14, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828086, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828088, "dur": 12, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828103, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828126, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828147, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828150, "dur": 21, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828175, "dur": 18, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828195, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828196, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828214, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828232, "dur": 46, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828281, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828302, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828303, "dur": 16, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828323, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828345, "dur": 18, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828366, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828384, "dur": 13, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828399, "dur": 15, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828416, "dur": 15, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828433, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828436, "dur": 13, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828451, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828492, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828512, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828532, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828551, "dur": 16, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828569, "dur": 15, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828587, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828605, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828622, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828643, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828691, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828709, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828711, "dur": 18, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828732, "dur": 15, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828750, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828769, "dur": 14, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828787, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828807, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828824, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828839, "dur": 14, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828856, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828907, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828928, "dur": 19, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828950, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828971, "dur": 15, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828988, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588828989, "dur": 13, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829005, "dur": 19, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829027, "dur": 16, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829046, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829066, "dur": 16, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829087, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829111, "dur": 42, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829155, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829174, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829194, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829212, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829231, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829248, "dur": 12, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829263, "dur": 14, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829279, "dur": 13, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829294, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829311, "dur": 44, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829357, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829374, "dur": 18, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829396, "dur": 16, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829415, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829435, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829453, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829474, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829497, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829499, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829519, "dur": 13, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829533, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829551, "dur": 47, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829600, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829625, "dur": 16, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829644, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829662, "dur": 15, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829679, "dur": 15, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829698, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829716, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829718, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829736, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829753, "dur": 14, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829769, "dur": 44, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829816, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829836, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829855, "dur": 15, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829871, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829873, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829891, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829909, "dur": 13, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829925, "dur": 16, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829944, "dur": 13, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829960, "dur": 14, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588829976, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830026, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830045, "dur": 100, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830149, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830171, "dur": 274, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830447, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830486, "dur": 5, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830493, "dur": 30, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830527, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830549, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830552, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830571, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830574, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830596, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830598, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830624, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830626, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830651, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830654, "dur": 19, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830675, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830676, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830694, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830696, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830713, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830715, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830734, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830736, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830757, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830760, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830778, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830780, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830800, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830802, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830834, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830837, "dur": 18, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830857, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830859, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830882, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830884, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830906, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830909, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830928, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830930, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830948, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830950, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830970, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830972, "dur": 18, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830992, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588830994, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831015, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831017, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831038, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831040, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831061, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831063, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831081, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831084, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831102, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831103, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831123, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831126, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831145, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831147, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831166, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831168, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831188, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831207, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831209, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831228, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831230, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831250, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831252, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831277, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831280, "dur": 20, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831302, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831304, "dur": 17, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831324, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831326, "dur": 16, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831344, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831346, "dur": 18, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831366, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831368, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831390, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831392, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831411, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831413, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831437, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831439, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831468, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831470, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831488, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831490, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831508, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831510, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831525, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831542, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831558, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831561, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831586, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831588, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831608, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831610, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831632, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831651, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831653, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831674, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831676, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831696, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831714, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831716, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831733, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831735, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831756, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831757, "dur": 13, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831773, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831792, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831811, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831813, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831833, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831835, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831856, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831858, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831884, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831886, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831912, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588831914, "dur": 106, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588832024, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588832047, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588832049, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588832072, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588832171, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588832194, "dur": 2552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834750, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834775, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834804, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834808, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834867, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834883, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834911, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588834930, "dur": 1069, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836003, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836019, "dur": 423, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836447, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836475, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836477, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836499, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836536, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836556, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836833, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836860, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588836985, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837011, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837014, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837035, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837037, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837065, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837081, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837118, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837133, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837211, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837242, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837243, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837259, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837395, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837417, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837435, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837455, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837468, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837532, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837556, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837576, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837596, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837625, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837653, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837672, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837696, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837698, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837713, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837730, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837805, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837822, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837823, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837842, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837888, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837903, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837968, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588837987, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838022, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838038, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838075, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838095, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838114, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838137, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838155, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838156, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838177, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838194, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838209, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838240, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838255, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838324, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838346, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838402, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838420, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838467, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838483, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838497, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838591, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838608, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838610, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838627, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838641, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838732, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838747, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838762, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838791, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838808, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838875, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838893, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838908, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838982, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588838998, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839026, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839047, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839075, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839088, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839091, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839105, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839136, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839151, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839165, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839181, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839195, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839209, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839223, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839238, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839256, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839271, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839306, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839324, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839344, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839361, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839378, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839390, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839408, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839424, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839440, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839474, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839497, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839511, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839557, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839571, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839587, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839602, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839636, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839648, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839679, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839696, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839717, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839735, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839766, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839782, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839982, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588839999, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840014, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840062, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840075, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840134, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840152, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840180, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840194, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840260, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840277, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840296, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840309, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840560, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840578, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840604, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840619, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840635, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840675, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840690, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840707, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840738, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588840754, "dur": 584, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841342, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841370, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841373, "dur": 35, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841412, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841430, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841462, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841480, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841502, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841518, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841624, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841641, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841654, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841682, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841697, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841716, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841730, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841774, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841806, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841827, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841842, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841860, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841896, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841912, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588841984, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842007, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842009, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842034, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842036, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842058, "dur": 210, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842272, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842297, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842299, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842329, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842351, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842491, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842511, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842677, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842699, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842808, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588842833, "dur": 356, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588843193, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588843210, "dur": 488, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588843702, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588843723, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588844066, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588844083, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588844437, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588844455, "dur": 32940, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588877401, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588877405, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588877436, "dur": 2920, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588880359, "dur": 758, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881122, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881138, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881157, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881159, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881184, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881197, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881261, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881278, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881279, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881300, "dur": 468, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881771, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588881785, "dur": 763, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882552, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882569, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882591, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882593, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882637, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882652, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882682, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882700, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882781, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882798, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882917, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588882938, "dur": 1061, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884003, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884015, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884043, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884057, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884104, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884128, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884130, "dur": 141, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884275, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884293, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884311, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884325, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884365, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884382, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588884395, "dur": 1116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885515, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885531, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885628, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885641, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885875, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885895, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885917, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588885938, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886040, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886053, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886155, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886169, "dur": 722, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886895, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588886931, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887127, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887143, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887185, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887198, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887385, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887406, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887470, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887485, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887547, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887567, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887619, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588887638, "dur": 738, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888381, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888407, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888409, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888591, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888606, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888726, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888744, "dur": 204, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888952, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588888965, "dur": 239, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588889207, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588889209, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588889226, "dur": 577, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588889808, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588889823, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890009, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890022, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890067, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890082, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890146, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890148, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890170, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890368, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890394, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890395, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890431, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890452, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890481, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890496, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890521, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890538, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890555, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890570, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890587, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890605, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890626, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890640, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890656, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890675, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890694, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890713, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890732, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890755, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890774, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890794, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890813, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890829, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890847, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890863, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890883, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890902, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890923, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890937, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890951, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890967, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588890982, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891002, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891019, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891036, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891055, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891083, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891098, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891113, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891132, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891134, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891157, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891175, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891177, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891196, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891198, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891216, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891236, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891254, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891266, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891276, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891291, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891308, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891324, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891326, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891343, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891360, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891361, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891382, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891400, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891402, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891419, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891421, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891443, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891445, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891463, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891465, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891487, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891489, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891511, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891513, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891529, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891545, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891546, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891566, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891586, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891588, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891609, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891611, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891630, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891632, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891656, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891658, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891677, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891678, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891698, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891722, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891836, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891863, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891865, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891890, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891913, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891931, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588891933, "dur": 282, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588892219, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588892237, "dur": 190, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588892433, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588892457, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720588892459, "dur": 859161, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589751635, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589751639, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589751684, "dur": 19, "ph": "X", "name": "ReadAsync 7081", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589751705, "dur": 6780, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589758488, "dur": 28, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589758519, "dur": 213, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753720589758734, "dur": 1312, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 20752, "tid": 868, "ts": 1753720589771377, "dur": 1403, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 8589934592, "ts": 1753720588797629, "dur": 72218, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 8589934592, "ts": 1753720588869851, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20752, "tid": 8589934592, "ts": 1753720588869858, "dur": 1116, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 868, "ts": 1753720589772782, "dur": 24, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20752, "tid": 4294967296, "ts": 1753720588785390, "dur": 975557, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753720588788769, "dur": 5116, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753720589760964, "dur": 3894, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753720589763634, "dur": 37, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753720589764922, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20752, "tid": 868, "ts": 1753720589772807, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753720588809873, "dur": 1168, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720588811049, "dur": 747, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720588811903, "dur": 62, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753720588811965, "dur": 264, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720588813408, "dur": 598, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_DDB1499AED0A17A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753720588814968, "dur": 1077, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753720588817994, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753720588812253, "dur": 19246, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720588831514, "dur": 923520, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720589755036, "dur": 264, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720589756575, "dur": 852, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753720588812402, "dur": 19119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588831540, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588831905, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588833032, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588833192, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588833377, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588833503, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588833725, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588833922, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588834130, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588834333, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588834538, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588834719, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588834936, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588835091, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588835301, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588835793, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588835982, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588836345, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588836660, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588836856, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588837053, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588837243, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588837488, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588837685, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588837916, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588838335, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588838485, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588838606, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588838917, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588839428, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588839707, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588839864, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588840304, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588840363, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588840449, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588840606, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588840724, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588840849, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588841191, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588841649, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588842075, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588842167, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753720588842316, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588842717, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588843150, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588843480, "dur": 37626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588881108, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588882635, "dur": 1403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753720588882619, "dur": 2915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588885586, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588887097, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588888652, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588890053, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588892141, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Stores.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753720588891472, "dur": 1438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753720588893111, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753720588893323, "dur": 861706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588812453, "dur": 19090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588831546, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753720588831923, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588831921, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_5A104ACC7C39F2CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753720588832854, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753720588833363, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588833742, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588833928, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588834141, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588834362, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588834800, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588835000, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588835198, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588835400, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588835940, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588836545, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Sequence\\SelectAndMoveItem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753720588837201, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Move\\MovingItems.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753720588836193, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588837743, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588837926, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588838327, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753720588838447, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588838990, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588839121, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753720588839326, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588839632, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588839615, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588840506, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588840262, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588841012, "dur": 1078, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588842133, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588842221, "dur": 919, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588842094, "dur": 1394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588843519, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588843792, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588844271, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588844653, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588845165, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588845530, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588845902, "dur": 36739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753720588884038, "dur": 8127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588892205, "dur": 962, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753720588882642, "dur": 10682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753720588893377, "dur": 861766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588812429, "dur": 19103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588831540, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588831622, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753720588831616, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A5D79986F9CBC457.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753720588831914, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753720588831913, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_917E5BD489C19DA6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753720588832331, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753720588832506, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753720588832636, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588832755, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588832855, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753720588832920, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753720588833368, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588833508, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588833732, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588833936, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588834143, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588834343, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588834557, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588834740, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588835176, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\ProjectGeneration\\ProjectProperties.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753720588834940, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588835777, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588835978, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588836336, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588837101, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int3x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753720588836934, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588837757, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588837942, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588838351, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753720588838521, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588839281, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753720588839000, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588839610, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753720588839721, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588840085, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588840686, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753720588840794, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588840942, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588841527, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588841603, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588842066, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588842139, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588842656, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588842929, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588843477, "dur": 37624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588881106, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588882584, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588882652, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588884016, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588884153, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588887309, "dur": 4936, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753720588885773, "dur": 6769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753720588893101, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753720588893168, "dur": 861865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588812447, "dur": 19090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588831540, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588831903, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753720588831902, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_82645536A6FD6826.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588832255, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753720588832505, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753720588832954, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753720588833369, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588833498, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588833734, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588833923, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588834129, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588834337, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588834526, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588834731, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588834960, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588835162, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588835362, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588835862, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588836061, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588836408, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588836680, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588836904, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588837107, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588837315, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588837447, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588837685, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588837918, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588838331, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588838460, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588838998, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588839090, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588839289, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588839451, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588839623, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588840258, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588840461, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588840611, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588840729, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588840877, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588841025, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588841148, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753720588841296, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588841531, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588841997, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588842075, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753720588842074, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588842546, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588842748, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588843302, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588843796, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588844138, "dur": 36965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588881104, "dur": 1593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588882697, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588884032, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753720588882760, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588884251, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588885647, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588885857, "dur": 1472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588887356, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.ShaderStripping.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588888718, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753720588888776, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588890204, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.ExampleCentral.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588891629, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753720588893164, "dur": 861871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588812471, "dur": 19078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588831552, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588831907, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753720588831906, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A49F289DC8D09E11.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588833361, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588833690, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588833891, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588834109, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588834317, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588834529, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588834725, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588835037, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588835258, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588835462, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588835961, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588836156, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588836509, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588836772, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588836972, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588837169, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588837737, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588837926, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588838526, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588838703, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588838868, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588839370, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588839492, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588839666, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588839801, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588839914, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588840243, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588840776, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588841050, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753720588841193, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588842133, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753720588841617, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588842213, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588842277, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588842731, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588843284, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588843750, "dur": 38878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588882629, "dur": 1455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588884121, "dur": 1421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588885583, "dur": 1367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588886978, "dur": 1339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588888390, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588889839, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588891267, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRLabs.AV3Manager.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753720588893140, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753720588893698, "dur": 861345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588812487, "dur": 19068, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588831559, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D2E6889350BDFFF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588831674, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588831911, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588831909, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588832103, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588832466, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588833360, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588833790, "dur": 736, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588834924, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753720588835464, "dur": 559, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753720588832240, "dur": 5187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588837487, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588837782, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588837920, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588838334, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588838464, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588838584, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588838997, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588839148, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588839375, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588839295, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588840549, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588840649, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753720588840798, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588841070, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588840775, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588841606, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588841467, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588842222, "dur": 782, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588842027, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588843363, "dur": 29080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588872445, "dur": 2845, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588875291, "dur": 5807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588881101, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588882597, "dur": 1447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753720588884050, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588885514, "dur": 1431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588887258, "dur": 4893, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753720588886981, "dur": 6284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753720588893309, "dur": 861728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588812509, "dur": 19054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588831566, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_DD36A0C7D3C71D14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588831915, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588831914, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F97977208FF60E8C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588832720, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588832850, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753720588833024, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588833245, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588833353, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588833500, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588833988, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588834205, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588834413, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588834998, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588835333, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588835839, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588836362, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588836713, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588837044, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588837287, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588837691, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588837917, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588838332, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588838466, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588838592, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588838705, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588838803, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588838856, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588839375, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588839013, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588839503, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588839647, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588839788, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588840408, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588840657, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753720588840897, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588840782, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588841757, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588842221, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588842098, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588842746, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588843183, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588843522, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588843950, "dur": 38669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753720588882635, "dur": 1766, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588882621, "dur": 3183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588887258, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.12\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588885833, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588887617, "dur": 1360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.param-introspection.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588889011, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588890431, "dur": 1372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588892205, "dur": 649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753720588891830, "dur": 2025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.vrchat.core.vpm-resolver.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753720588893905, "dur": 861141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588812529, "dur": 19042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588831587, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753720588831574, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_ED9976D1F71D37ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588831904, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588832033, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_974F4966AAA7928B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588833356, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588833483, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588833698, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588833889, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588834088, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588834291, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588834484, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588834678, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588834884, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588835081, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588835289, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588835784, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588835994, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588836520, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588836712, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588837004, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588837419, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588837792, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588837926, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588838329, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588838453, "dur": 564, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588839018, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588839478, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588839608, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588840236, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.xr.management@4.4.0\\Editor\\XRCustomLoaderUIManager.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753720588839786, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588840485, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588840566, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588840697, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588840846, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588840961, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588841065, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588841189, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588841727, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588842371, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588842786, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588842874, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753720588842969, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588843458, "dur": 31835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588875293, "dur": 6564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753720588881859, "dur": 1346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588884033, "dur": 4034, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753720588883237, "dur": 5317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588888588, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588890075, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.vrchat.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588892141, "dur": 985, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753720588891532, "dur": 2123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753720588893689, "dur": 861354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588812552, "dur": 19026, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588831581, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CABBC8F7A32A378E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588831690, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588831916, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588831915, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8C5E2A3FA2C2B638.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588832101, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588832589, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588832670, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588833217, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588833341, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588835465, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\TestRunner\\TestPlatform.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753720588832220, "dur": 3956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588836256, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588837256, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEditor.TestRunner\\GUI\\IGuiHelper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753720588837468, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEditor.TestRunner\\GUI\\TestRunnerResult.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753720588836374, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588837932, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588837999, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588838324, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588838529, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588839152, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588839281, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588839272, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588839847, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588839951, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588840080, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588840200, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588840344, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588840493, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588840728, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588840892, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588841146, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588841732, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588842181, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588842259, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588842758, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753720588843135, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588843316, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588843745, "dur": 37345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588881102, "dur": 1431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588882578, "dur": 1523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588884101, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588884383, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588885795, "dur": 1406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588887202, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588887338, "dur": 1657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588888996, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588889385, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753720588889085, "dur": 1541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.harmony-patches.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588890671, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753720588892089, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588892165, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588893121, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753720588893384, "dur": 861657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588812571, "dur": 19014, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588831909, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753720588831908, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_48F6D28C1C558B25.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753720588832645, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588833041, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753720588833092, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588833279, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14776904114713646495.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753720588833386, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588833632, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588833870, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588834057, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588834262, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588834461, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588834664, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588834861, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588835236, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588835776, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588835985, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588836681, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588837092, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588837336, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588837684, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588837922, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588838331, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753720588838462, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588839086, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_3152B6A9836FBF3B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753720588839170, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588839702, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588839953, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753720588840454, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588841186, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588841631, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588841727, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588842273, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588842656, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588843123, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588843470, "dur": 37626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588881098, "dur": 1592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588882729, "dur": 1342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588884116, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588887308, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.12\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753720588885742, "dur": 1720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588887502, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588888934, "dur": 1438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588890416, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753720588892384, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588892964, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753720588893150, "dur": 861881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588812599, "dur": 18991, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588831911, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588832179, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588832253, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753720588832456, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753720588832521, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588832608, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753720588832922, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753720588833357, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588833503, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588833768, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588833963, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588834176, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588834378, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588834594, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588834799, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588834997, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588835222, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588835423, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588835942, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588836153, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588836548, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588836767, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588837032, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588837223, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588837758, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588837932, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588838337, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753720588838457, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753720588838931, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753720588839433, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753720588839605, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753720588839995, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588840058, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753720588840829, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753720588840966, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753720588841104, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753720588841235, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753720588841683, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588841767, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753720588842629, "dur": 74, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753720588842738, "dur": 910528, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588812612, "dur": 18983, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588831649, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753720588831923, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1753720588831595, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AF9F947519E8CC62.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753720588832081, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753720588833083, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588833378, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588833511, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588833727, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588833924, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588834132, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588834341, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588834581, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588834778, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588834995, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588835222, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588835438, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588835938, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588836544, "dur": 1082, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\Drawers\\Layers\\MarkersLayer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753720588836133, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588837726, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588837919, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588838326, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753720588838499, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753720588838449, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588839185, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753720588839354, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588840098, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753720588840215, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588840620, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588841070, "dur": 541, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753720588840838, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1753720588841869, "dur": 102, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588842235, "dur": 36636, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1753720588881091, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588882591, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/FbxBuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588884016, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588885396, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588885469, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588886849, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588887383, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588888699, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588888759, "dur": 1388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588890191, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588891615, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753720588893145, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753720588893912, "dur": 861126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753720589759868, "dur": 1375, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20752, "tid": 868, "ts": 1753720589773142, "dur": 1770, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20752, "tid": 868, "ts": 1753720589774948, "dur": 1699, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20752, "tid": 868, "ts": 1753720589769420, "dur": 7864, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}