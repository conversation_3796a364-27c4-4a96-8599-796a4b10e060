{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20752, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20752, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20752, "tid": 2307, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20752, "tid": 2307, "ts": 1753724547838319, "dur": 682, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547842571, "dur": 608, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20752, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546224249, "dur": 14967, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546239218, "dur": 1594140, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546239229, "dur": 19, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546239251, "dur": 125286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546364548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546364551, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546364583, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546364590, "dur": 1715, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366311, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366336, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366337, "dur": 26, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366367, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366369, "dur": 36, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366407, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366409, "dur": 18, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366429, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366431, "dur": 11, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366444, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366464, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366491, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366494, "dur": 22, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366519, "dur": 22, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366543, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366567, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366569, "dur": 23, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366594, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366596, "dur": 30, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366629, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366655, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366657, "dur": 21, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366681, "dur": 23, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366706, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366708, "dur": 21, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366731, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366733, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366754, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366774, "dur": 22, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366800, "dur": 23, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366825, "dur": 17, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366845, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366846, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366871, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366873, "dur": 19, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366893, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366895, "dur": 31, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366928, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366952, "dur": 16, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366971, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366991, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546366992, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367014, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367036, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367056, "dur": 16, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367075, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367096, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367115, "dur": 15, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367133, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367158, "dur": 18, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367179, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367196, "dur": 16, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367215, "dur": 15, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367233, "dur": 15, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367251, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367271, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367294, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367315, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367316, "dur": 23, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367343, "dur": 22, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367368, "dur": 17, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367387, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367389, "dur": 23, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367414, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367415, "dur": 15, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367433, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367457, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367481, "dur": 15, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367499, "dur": 16, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367517, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367536, "dur": 17, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367554, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367556, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367578, "dur": 15, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367596, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367614, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367633, "dur": 15, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367651, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367669, "dur": 16, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367687, "dur": 19, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367709, "dur": 16, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367728, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367748, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367750, "dur": 16, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367768, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367787, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367788, "dur": 17, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367808, "dur": 15, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367825, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367848, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367867, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367887, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367909, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367911, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367932, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367934, "dur": 25, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367962, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367965, "dur": 31, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546367999, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368025, "dur": 13, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368049, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368076, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368078, "dur": 30, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368110, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368112, "dur": 19, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368135, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368163, "dur": 21, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368187, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368189, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368212, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368232, "dur": 15, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368250, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368272, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368296, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368317, "dur": 22, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368341, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368343, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368365, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368367, "dur": 12, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368381, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368398, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368417, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368419, "dur": 18, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368440, "dur": 16, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368459, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368477, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368478, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368496, "dur": 33, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368531, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368548, "dur": 41, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368591, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368610, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368629, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368648, "dur": 15, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368665, "dur": 16, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368683, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368685, "dur": 14, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368702, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368723, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368741, "dur": 16, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368759, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368780, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368800, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368818, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368838, "dur": 16, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368856, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368874, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368893, "dur": 17, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368914, "dur": 18, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368934, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368953, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368972, "dur": 18, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546368992, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369008, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369027, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369049, "dur": 22, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369073, "dur": 15, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369091, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369111, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369129, "dur": 16, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369148, "dur": 19, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369170, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369194, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369218, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369235, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369237, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369256, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369276, "dur": 18, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369296, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369313, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369332, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369333, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369353, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369355, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369375, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369398, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369401, "dur": 21, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369424, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369426, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369448, "dur": 19, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369468, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369470, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369489, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369509, "dur": 15, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369527, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369545, "dur": 16, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369564, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369587, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369607, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369628, "dur": 17, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369648, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369673, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369693, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369712, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369733, "dur": 16, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369752, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369773, "dur": 17, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369793, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369811, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369829, "dur": 16, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369846, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369848, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369866, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369887, "dur": 16, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369906, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369928, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369958, "dur": 19, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369978, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369980, "dur": 15, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546369997, "dur": 18, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370017, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370037, "dur": 13, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370052, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370073, "dur": 16, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370092, "dur": 16, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370111, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370130, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370152, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370169, "dur": 16, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370187, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370205, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370207, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370224, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370244, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370266, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370284, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370305, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370324, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370347, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370371, "dur": 16, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370392, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370412, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370430, "dur": 32, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370465, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370490, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370511, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370513, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370532, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370554, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370555, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370576, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370594, "dur": 16, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370614, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370631, "dur": 17, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370651, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370670, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370689, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370707, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370728, "dur": 20, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370754, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370777, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370779, "dur": 17, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370799, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370821, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370839, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370857, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370876, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370899, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370901, "dur": 20, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370922, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370924, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370941, "dur": 16, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370959, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370977, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370979, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546370996, "dur": 18, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371017, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371036, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371059, "dur": 14, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371076, "dur": 22, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371099, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371101, "dur": 16, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371120, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371141, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371159, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371161, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371181, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371182, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371202, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371221, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371242, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371261, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371280, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371282, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371303, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371323, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371340, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371358, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371377, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371397, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371399, "dur": 17, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371419, "dur": 16, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371438, "dur": 17, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371458, "dur": 15, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371475, "dur": 18, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371496, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371512, "dur": 13, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371527, "dur": 16, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371546, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371564, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371582, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371603, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371605, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371624, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371644, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371663, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371683, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371703, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371721, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371722, "dur": 17, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371741, "dur": 20, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371764, "dur": 15, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371782, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371801, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371820, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371835, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371837, "dur": 16, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371856, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371875, "dur": 14, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371892, "dur": 13, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371908, "dur": 14, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371925, "dur": 16, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371944, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371963, "dur": 13, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371979, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546371999, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372019, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372042, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372064, "dur": 18, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372085, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372108, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372110, "dur": 33, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372149, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372176, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372178, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372207, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372237, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372251, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372267, "dur": 13, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372282, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372305, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372331, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372348, "dur": 18, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372369, "dur": 18, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372390, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372420, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372441, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372460, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372462, "dur": 23, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372488, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372490, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372508, "dur": 28, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372539, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372561, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372582, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372603, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372604, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372630, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372651, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372653, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372673, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372674, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372693, "dur": 12, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372707, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372737, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372757, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372775, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372796, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372816, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372842, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372844, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372864, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372884, "dur": 21, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372907, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372927, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372954, "dur": 14, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372970, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546372988, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373012, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373039, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373059, "dur": 15, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373076, "dur": 18, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373096, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373116, "dur": 19, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373137, "dur": 19, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373158, "dur": 16, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373177, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373210, "dur": 24, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373236, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373238, "dur": 17, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373258, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373282, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373284, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373309, "dur": 18, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373330, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373350, "dur": 18, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373371, "dur": 18, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373391, "dur": 15, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373409, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373423, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373425, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373446, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373448, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373467, "dur": 14, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373484, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373486, "dur": 157, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373648, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373692, "dur": 2, "ph": "X", "name": "ProcessMessages 2278", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373695, "dur": 22, "ph": "X", "name": "ReadAsync 2278", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373719, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373721, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373742, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373743, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373762, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373782, "dur": 16, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373801, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373802, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373820, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373822, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373841, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373862, "dur": 18, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373883, "dur": 16, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373902, "dur": 17, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373921, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373943, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373965, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373983, "dur": 13, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546373999, "dur": 21, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374023, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374046, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374069, "dur": 13, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374085, "dur": 16, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374104, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374124, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374148, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374168, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374193, "dur": 16, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374211, "dur": 19, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374275, "dur": 21, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374297, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374299, "dur": 16, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374317, "dur": 19, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374338, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374341, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374361, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374363, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374383, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374404, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374421, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374440, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374459, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374479, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374501, "dur": 27, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374530, "dur": 15, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374548, "dur": 18, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374569, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374587, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374607, "dur": 14, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374624, "dur": 15, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374641, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374658, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374682, "dur": 15, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374700, "dur": 20, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374723, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374742, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374763, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374780, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374782, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374799, "dur": 14, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374815, "dur": 14, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374832, "dur": 20, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374854, "dur": 17, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374874, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374892, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374894, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374916, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374934, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374952, "dur": 14, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374969, "dur": 15, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546374986, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375041, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375062, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375081, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375100, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375118, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375137, "dur": 18, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375158, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375177, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375197, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375221, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375240, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375261, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375282, "dur": 14, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375299, "dur": 23, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375324, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375344, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375363, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375383, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375401, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375419, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375443, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375444, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375464, "dur": 16, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375482, "dur": 15, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375499, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375501, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375521, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375539, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375557, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375584, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375610, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375612, "dur": 19, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375635, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375657, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375658, "dur": 23, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375685, "dur": 20, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375707, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375728, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375729, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375750, "dur": 15, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375768, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375788, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375810, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375829, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375848, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375850, "dur": 18, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375871, "dur": 35, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375908, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375928, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546375951, "dur": 122, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376075, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376095, "dur": 15, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376112, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376136, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376138, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376157, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376159, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376180, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376201, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376220, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376238, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376255, "dur": 16, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376274, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376297, "dur": 16, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376316, "dur": 16, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376335, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376360, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376379, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376381, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376403, "dur": 15, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376421, "dur": 17, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376441, "dur": 14, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376458, "dur": 15, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376476, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376503, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376521, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376539, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376541, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376558, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376582, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376601, "dur": 13, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376616, "dur": 15, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376634, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376652, "dur": 17, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376671, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376673, "dur": 18, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376694, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376712, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376729, "dur": 14, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376747, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376768, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376787, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376808, "dur": 15, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376826, "dur": 17, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376846, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376865, "dur": 18, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376886, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376903, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376905, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376926, "dur": 17, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376945, "dur": 15, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376963, "dur": 14, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376980, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546376997, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377020, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377042, "dur": 15, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377060, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377078, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377097, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377115, "dur": 13, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377130, "dur": 16, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377149, "dur": 13, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377165, "dur": 20, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377188, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377204, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377226, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377228, "dur": 15, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377244, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377246, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377272, "dur": 13, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377289, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377308, "dur": 15, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377325, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377348, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377367, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377369, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377388, "dur": 15, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377408, "dur": 18, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377428, "dur": 15, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377446, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377462, "dur": 16, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377483, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377503, "dur": 14, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377520, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377539, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377557, "dur": 17, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377576, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377595, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377614, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377631, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377633, "dur": 17, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377653, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377671, "dur": 15, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377689, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377708, "dur": 14, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377724, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377744, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377760, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377776, "dur": 14, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377793, "dur": 16, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377811, "dur": 19, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377833, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377851, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377869, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377886, "dur": 15, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377904, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377923, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377940, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377961, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377978, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546377998, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378017, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378034, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378052, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378072, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378090, "dur": 14, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378107, "dur": 14, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378123, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378145, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378163, "dur": 14, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378180, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378197, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378229, "dur": 26, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378258, "dur": 17, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378276, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378278, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378297, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378313, "dur": 16, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378332, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378348, "dur": 15, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378366, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378383, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378401, "dur": 15, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378420, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378438, "dur": 16, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378457, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378476, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378478, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378497, "dur": 15, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378513, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378515, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378533, "dur": 20, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378555, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378573, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378590, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378609, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378627, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378646, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378665, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378686, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378687, "dur": 16, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378706, "dur": 17, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378726, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378744, "dur": 15, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378761, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378778, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378797, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378814, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378816, "dur": 13, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378831, "dur": 16, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378850, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378875, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378893, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378912, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378935, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378953, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378972, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546378993, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379012, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379030, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379048, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379066, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379084, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379102, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379119, "dur": 12, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379134, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379149, "dur": 16, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379167, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379186, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379210, "dur": 15, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379229, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379249, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379266, "dur": 14, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379283, "dur": 15, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379301, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379318, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379340, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379359, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379377, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379395, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379411, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379413, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379433, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379451, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379468, "dur": 14, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379484, "dur": 13, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379500, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379518, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379537, "dur": 15, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379555, "dur": 17, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379574, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379593, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379609, "dur": 18, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379629, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379645, "dur": 15, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379663, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379680, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379698, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379718, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379737, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379760, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379778, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379797, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379815, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379835, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379854, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379874, "dur": 15, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379892, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379910, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379928, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379948, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379950, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379966, "dur": 14, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546379982, "dur": 15, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380000, "dur": 12, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380014, "dur": 13, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380030, "dur": 17, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380050, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380067, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380069, "dur": 35, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380105, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380106, "dur": 13, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380122, "dur": 22, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380145, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380147, "dur": 15, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380164, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380181, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380198, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380216, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380236, "dur": 16, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380255, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380275, "dur": 17, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380295, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380312, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380328, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380330, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380346, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380363, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380380, "dur": 15, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380398, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380417, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380436, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380437, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380458, "dur": 16, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380476, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380494, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380510, "dur": 14, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380528, "dur": 12, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380542, "dur": 17, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380562, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380580, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380600, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380619, "dur": 15, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380636, "dur": 15, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380652, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380654, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380672, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380689, "dur": 12, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380704, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380723, "dur": 18, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380743, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380745, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380764, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380766, "dur": 15, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380784, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380802, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380820, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380838, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380857, "dur": 13, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380874, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380889, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380907, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380937, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380938, "dur": 15, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380956, "dur": 16, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380974, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546380993, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381012, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381030, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381047, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381067, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381087, "dur": 16, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381105, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381125, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381143, "dur": 15, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381160, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381178, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381196, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381212, "dur": 14, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381228, "dur": 13, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381243, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381263, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381280, "dur": 15, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381298, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381317, "dur": 15, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381333, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381335, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381352, "dur": 15, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381370, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381386, "dur": 14, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381402, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381418, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381434, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381436, "dur": 61, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381502, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381529, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381530, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381553, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381555, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381576, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381577, "dur": 23, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381602, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381604, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381631, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381655, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381657, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381681, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381683, "dur": 19, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381704, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381706, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381739, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381741, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381768, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381771, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381796, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381798, "dur": 19, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381819, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381821, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381843, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381845, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381869, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381905, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381929, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381932, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381955, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381957, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381983, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546381987, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382010, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382013, "dur": 14, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382028, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382030, "dur": 18, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382050, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382052, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382076, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382078, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382098, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382100, "dur": 15, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382116, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382118, "dur": 15, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382135, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382136, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382157, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382159, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382177, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382178, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382195, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382211, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382213, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382236, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382238, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382261, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382264, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382288, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382290, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382307, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382309, "dur": 17, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382328, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382330, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382352, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382354, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382374, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382377, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382398, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382400, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382421, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382423, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382440, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382442, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382462, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382464, "dur": 18, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382484, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382487, "dur": 18, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382507, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382509, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382524, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382526, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382547, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382549, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382566, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382568, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382587, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382589, "dur": 22, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382613, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382616, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382640, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382642, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382666, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382668, "dur": 20, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382691, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382693, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382718, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382721, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382746, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382748, "dur": 20, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382769, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382772, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382796, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382799, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382824, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382826, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382848, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382850, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382876, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382878, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382898, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382900, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382919, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382921, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382942, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382962, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382964, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546382982, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383000, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383002, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383018, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383020, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383037, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383058, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383060, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383082, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383103, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383105, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383121, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383139, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383159, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383161, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383181, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383204, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383297, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383319, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383341, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383343, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383358, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383375, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383377, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383395, "dur": 199, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383599, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546383620, "dur": 915, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546384538, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546384558, "dur": 2763, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387324, "dur": 26, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387353, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387355, "dur": 519, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387879, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387897, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387915, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387967, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546387989, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388228, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388248, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388365, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388389, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388391, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388412, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388414, "dur": 10, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388427, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388514, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388528, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388547, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388564, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388655, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388674, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388821, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388924, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388945, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546388947, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389087, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389090, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389105, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389133, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389200, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389224, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389243, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389262, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389275, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389292, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389366, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389380, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389474, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389491, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389517, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389529, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389554, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389557, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389572, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389592, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389607, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389619, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389640, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389651, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389728, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389748, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389761, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389795, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389809, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389879, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389907, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389909, "dur": 11, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546389922, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390023, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390039, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390058, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390264, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390277, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390298, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390510, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390526, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390528, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390558, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390571, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390606, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390624, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390641, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390700, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390713, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390735, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390764, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390780, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390805, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390820, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390822, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390840, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390929, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390942, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390960, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546390982, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391043, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391058, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391077, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391125, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391137, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391174, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391196, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391218, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391262, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391281, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391299, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391312, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391349, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391368, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391446, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391464, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391482, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391484, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391535, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391548, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391567, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391583, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391599, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391672, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391685, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391738, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391766, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391947, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391966, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546391984, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392001, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392036, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392038, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392053, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392068, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392082, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392128, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392192, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392216, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392218, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392239, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392331, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392347, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392350, "dur": 100, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392454, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392467, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392526, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392549, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392570, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392595, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392614, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392648, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392662, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392678, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392695, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392711, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392791, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546392806, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393006, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393022, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393039, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393065, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393086, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393088, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393124, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393141, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393182, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393193, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393237, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393251, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393263, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393277, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393332, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393351, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393368, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393390, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393407, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393425, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393440, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393621, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393646, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393669, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393781, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393806, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393822, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393841, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393858, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546393876, "dur": 316, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546394196, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546394218, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546394419, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546394435, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546394635, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546394656, "dur": 1493, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546396153, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546396179, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546396181, "dur": 39040, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546435230, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546435235, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546435257, "dur": 22, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546435280, "dur": 3282, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438567, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438569, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438603, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438607, "dur": 73, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438683, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438703, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438789, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546438820, "dur": 1146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546439971, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546439988, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440062, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440077, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440142, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440204, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440222, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440321, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546440344, "dur": 733, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441082, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441109, "dur": 451, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441565, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441589, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441591, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441613, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441629, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441650, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441652, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441804, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441819, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441981, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546441998, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546442022, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546442041, "dur": 548, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546442592, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546442603, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546442925, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546442939, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443150, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443152, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443168, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443186, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443311, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443332, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443345, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443347, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443407, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443424, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443426, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443534, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443550, "dur": 272, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443825, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546443838, "dur": 563, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444405, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444426, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444701, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444728, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444751, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444768, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444845, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444865, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444879, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546444890, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445110, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445126, "dur": 462, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445590, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445606, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445901, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445917, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445963, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546445966, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446032, "dur": 13, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446047, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446282, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446302, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446321, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446405, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446419, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446540, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446553, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446590, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446602, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446618, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446639, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446642, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446658, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446675, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446691, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446703, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446721, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446737, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446771, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446784, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446799, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446812, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446828, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446847, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446863, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446881, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446899, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446913, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446930, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446942, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446957, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446969, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546446987, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447003, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447015, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447019, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447035, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447052, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447063, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447081, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447097, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447108, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447127, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447145, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447164, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447166, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447180, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447215, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447227, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447259, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447279, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447298, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447300, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447313, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447330, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447357, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447377, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447379, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447394, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447413, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447414, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447433, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447448, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447463, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447484, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447499, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447502, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447518, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447519, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447536, "dur": 14, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447553, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447573, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447591, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447593, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447612, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447614, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447631, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447632, "dur": 13, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447648, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447665, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447690, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447692, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447710, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447741, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447760, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447778, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447799, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447823, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447850, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447852, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447877, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447878, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447941, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447960, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546447988, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448011, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448034, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448052, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448055, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448079, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448099, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448195, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448220, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448235, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448286, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448311, "dur": 176, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448492, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724546448514, "dur": 878624, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547327146, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547327149, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547327180, "dur": 5337, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547332520, "dur": 198950, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547531479, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547531483, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547531510, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547531515, "dur": 81429, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547612953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547612957, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547612986, "dur": 25, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547613012, "dur": 5364, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547618382, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547618385, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547618400, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547618402, "dur": 1006, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547619415, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547619418, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547619435, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547619457, "dur": 207444, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547826910, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547826914, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547826939, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547826943, "dur": 500, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547827448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547827451, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547827489, "dur": 31, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547827522, "dur": 462, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547827989, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547828015, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20752, "tid": 21474836480, "ts": 1753724547828017, "dur": 5335, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547843184, "dur": 1713, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 17179869184, "ts": 1753724546224183, "dur": 5, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753724546224189, "dur": 15028, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20752, "tid": 17179869184, "ts": 1753724546239218, "dur": 28, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547844899, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20752, "tid": 1, "ts": 1753724545600776, "dur": 4475, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753724545605253, "dur": 22897, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20752, "tid": 1, "ts": 1753724545628164, "dur": 30815, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547844909, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 20752, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545599026, "dur": 65583, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545664610, "dur": 25423, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545665912, "dur": 1531, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545667447, "dur": 1446, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545668895, "dur": 165, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669062, "dur": 10, "ph": "X", "name": "ProcessMessages 20517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669073, "dur": 25, "ph": "X", "name": "ReadAsync 20517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669100, "dur": 18, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669121, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669140, "dur": 48, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669193, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669217, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669218, "dur": 77, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669299, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669330, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669333, "dur": 27, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669363, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669387, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669389, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669416, "dur": 18, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669436, "dur": 19, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669461, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669489, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669490, "dur": 22, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669515, "dur": 19, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669536, "dur": 17, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669556, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669573, "dur": 16, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669592, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669614, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669642, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669644, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669668, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669685, "dur": 15, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669704, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669728, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669730, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669752, "dur": 13, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669767, "dur": 14, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669784, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669807, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669829, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669850, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669872, "dur": 16, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669891, "dur": 13, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669906, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669922, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669945, "dur": 13, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669960, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669976, "dur": 20, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545669998, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670018, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670020, "dur": 11, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670033, "dur": 13, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670048, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670071, "dur": 18, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670091, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670108, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670111, "dur": 19, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670133, "dur": 17, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670152, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670169, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670186, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670206, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670227, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670247, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670266, "dur": 14, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670283, "dur": 15, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670300, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670320, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670339, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670358, "dur": 19, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670378, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670380, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670398, "dur": 14, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670414, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670436, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670454, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670476, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670494, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670516, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670531, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670550, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670569, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670590, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670608, "dur": 18, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670629, "dur": 15, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670647, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670668, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670692, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670694, "dur": 18, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670715, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670740, "dur": 16, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670758, "dur": 14, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670776, "dur": 15, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670793, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670812, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670835, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670859, "dur": 16, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670877, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670893, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670915, "dur": 20, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670937, "dur": 16, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670956, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670976, "dur": 16, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545670995, "dur": 13, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671012, "dur": 17, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671031, "dur": 16, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671050, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671069, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671087, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671107, "dur": 13, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671122, "dur": 15, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671140, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671156, "dur": 15, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671173, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671193, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671212, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671229, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671248, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671270, "dur": 16, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671288, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671306, "dur": 28, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671337, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671359, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671381, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671400, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671421, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671423, "dur": 19, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671444, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671446, "dur": 29, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671478, "dur": 19, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671498, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671499, "dur": 17, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671518, "dur": 17, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671538, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671559, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671579, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671597, "dur": 16, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671615, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671616, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671634, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671653, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671674, "dur": 15, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671692, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671715, "dur": 15, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671732, "dur": 18, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671753, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671771, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671789, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671810, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671829, "dur": 14, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671846, "dur": 16, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671864, "dur": 16, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671884, "dur": 18, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671904, "dur": 34, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671940, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671960, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671979, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545671999, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672017, "dur": 259, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672281, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672347, "dur": 4, "ph": "X", "name": "ProcessMessages 4909", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672352, "dur": 24, "ph": "X", "name": "ReadAsync 4909", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672377, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672379, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672408, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672410, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672438, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672440, "dur": 21, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672464, "dur": 24, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672493, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672495, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672521, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672548, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672551, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672578, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672601, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672631, "dur": 27, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672660, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672663, "dur": 28, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672694, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672695, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672722, "dur": 17, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672742, "dur": 22, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672766, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672769, "dur": 25, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672802, "dur": 17, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672821, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672823, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672847, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672849, "dur": 25, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672876, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672877, "dur": 18, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672897, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672920, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672942, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672965, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672968, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545672992, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673011, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673063, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673086, "dur": 23, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673112, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673137, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673139, "dur": 60, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673202, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673226, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673246, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673248, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673273, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673275, "dur": 21, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673299, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673320, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673339, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673358, "dur": 15, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673376, "dur": 41, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673419, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673439, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673456, "dur": 16, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673474, "dur": 17, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673494, "dur": 47, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673543, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673563, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673583, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673749, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673752, "dur": 34, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673787, "dur": 2, "ph": "X", "name": "ProcessMessages 2238", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673790, "dur": 17, "ph": "X", "name": "ReadAsync 2238", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673810, "dur": 17, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673830, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673849, "dur": 19, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673871, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673888, "dur": 18, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673909, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673959, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673979, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545673998, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674017, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674018, "dur": 42, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674064, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674086, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674112, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674114, "dur": 11, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674127, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674169, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674189, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674210, "dur": 14, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674226, "dur": 13, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674242, "dur": 41, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674286, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674305, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674307, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674329, "dur": 15, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674346, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674390, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674412, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674429, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674447, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674464, "dur": 32, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674499, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674520, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674542, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674561, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674599, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674619, "dur": 16, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674637, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674653, "dur": 17, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674673, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674711, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674734, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674752, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674754, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674773, "dur": 56, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674831, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674852, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674870, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674873, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674893, "dur": 46, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674941, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674964, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545674982, "dur": 15, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675000, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675046, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675065, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675085, "dur": 17, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675104, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675107, "dur": 14, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675124, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675182, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675207, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675231, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675248, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675249, "dur": 54, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675306, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675329, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675349, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675351, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675369, "dur": 53, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675424, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675449, "dur": 16, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675467, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675468, "dur": 15, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675486, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675537, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675561, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675563, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675593, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675595, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675620, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675645, "dur": 22, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675670, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675690, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675709, "dur": 20, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675733, "dur": 38, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675781, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675808, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675809, "dur": 19, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675831, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675850, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675851, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675874, "dur": 15, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675938, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675960, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545675962, "dur": 37, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676002, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676024, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676043, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676061, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676079, "dur": 14, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676097, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676118, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676119, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676138, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676155, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676205, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676230, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676245, "dur": 14, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676262, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676285, "dur": 16, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676303, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676324, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676346, "dur": 13, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676362, "dur": 19, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676383, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676431, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676451, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676469, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676490, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676508, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676509, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676531, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676551, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676576, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676594, "dur": 14, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676612, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676646, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676649, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676669, "dur": 16, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676688, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676705, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676707, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676729, "dur": 17, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676748, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676767, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676768, "dur": 15, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676786, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676803, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676804, "dur": 12, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676819, "dur": 75, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676898, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676920, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676938, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676960, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676979, "dur": 17, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545676999, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677017, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677034, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677051, "dur": 15, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677068, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677108, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677127, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677145, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677165, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677181, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677183, "dur": 27, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677213, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677236, "dur": 15, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677254, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677272, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677288, "dur": 15, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677305, "dur": 39, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677347, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677366, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677385, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677405, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677426, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677444, "dur": 16, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677465, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677482, "dur": 17, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677501, "dur": 44, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677548, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677566, "dur": 17, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677586, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677606, "dur": 16, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677624, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677645, "dur": 16, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677665, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677683, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677702, "dur": 13, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677719, "dur": 35, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677756, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677775, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677793, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677812, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677834, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677836, "dur": 14, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677854, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677873, "dur": 16, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677892, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677910, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545677912, "dur": 202, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678116, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678119, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678139, "dur": 52, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678194, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678215, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678233, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678253, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678271, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678273, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678291, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678309, "dur": 16, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678327, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678343, "dur": 14, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678360, "dur": 14, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678377, "dur": 48, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678428, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678448, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678464, "dur": 15, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678481, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678500, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678520, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678540, "dur": 16, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678558, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678581, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678598, "dur": 15, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678615, "dur": 13, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678631, "dur": 40, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678673, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678696, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678713, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678715, "dur": 15, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678734, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678778, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678795, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678800, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678816, "dur": 20, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678839, "dur": 17, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678858, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678861, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678884, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678908, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678926, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678944, "dur": 14, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545678961, "dur": 38, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679001, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679023, "dur": 12, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679036, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679053, "dur": 15, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679070, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679112, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679137, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679139, "dur": 24, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679166, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679183, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679206, "dur": 17, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679226, "dur": 26, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679254, "dur": 16, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679275, "dur": 13, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679291, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679308, "dur": 15, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679326, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679373, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679397, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679399, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679417, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679419, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679439, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679488, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679512, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679533, "dur": 15, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679551, "dur": 51, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679604, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679624, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679644, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679664, "dur": 44, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679712, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679734, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679736, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679756, "dur": 17, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679775, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679792, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679794, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679810, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679813, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679836, "dur": 9, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679847, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679863, "dur": 15, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679880, "dur": 40, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679922, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679942, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679959, "dur": 17, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679978, "dur": 15, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545679997, "dur": 39, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680038, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680057, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680059, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680078, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680099, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680119, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680139, "dur": 17, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680158, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680174, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680192, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680237, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680256, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680270, "dur": 12, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680285, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680304, "dur": 16, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680323, "dur": 15, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680341, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680359, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680376, "dur": 16, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680395, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680440, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680460, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680481, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680500, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680518, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680537, "dur": 16, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680556, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680575, "dur": 19, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680597, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680615, "dur": 16, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680633, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680688, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680711, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680712, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680739, "dur": 15, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680757, "dur": 34, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680794, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680812, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680831, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680833, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680852, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680871, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680872, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680895, "dur": 14, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680912, "dur": 18, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680932, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680948, "dur": 16, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545680967, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681010, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681029, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681049, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681069, "dur": 40, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681112, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681134, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681136, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681155, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681174, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681193, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681211, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681228, "dur": 16, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681247, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681264, "dur": 12, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681279, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681315, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681338, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681363, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681383, "dur": 18, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681404, "dur": 15, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681420, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681422, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681439, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681456, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681476, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681528, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681547, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681566, "dur": 16, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681585, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681603, "dur": 12, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681617, "dur": 14, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681634, "dur": 18, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681654, "dur": 14, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681671, "dur": 16, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681690, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681737, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681759, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681761, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681778, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681779, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681798, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681819, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681838, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681856, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681873, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681890, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681908, "dur": 40, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681950, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681972, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681974, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681994, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545681996, "dur": 16, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682014, "dur": 13, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682029, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682048, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682071, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682090, "dur": 16, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682109, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682150, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682164, "dur": 12, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682180, "dur": 11, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682193, "dur": 15, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682210, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682230, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682251, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682274, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682291, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682310, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682358, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682377, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682396, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682415, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682434, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682453, "dur": 20, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682476, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682493, "dur": 14, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682509, "dur": 14, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682526, "dur": 40, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682568, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682589, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682610, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682629, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682648, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682667, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682686, "dur": 15, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682703, "dur": 15, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682721, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682765, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682785, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682803, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682820, "dur": 17, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682838, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682840, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682860, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682881, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682901, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682904, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682927, "dur": 19, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682949, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545682988, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683008, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683026, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683044, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683062, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683063, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683080, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683083, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683103, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683127, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683145, "dur": 13, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683160, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683197, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683219, "dur": 17, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683240, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683258, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683278, "dur": 16, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683297, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683315, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683333, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683351, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683397, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683415, "dur": 16, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683433, "dur": 19, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683455, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683473, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683492, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683512, "dur": 15, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683530, "dur": 13, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683545, "dur": 14, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683562, "dur": 37, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683602, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683620, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683643, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683664, "dur": 15, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683680, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683682, "dur": 16, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683699, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683701, "dur": 16, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683719, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683736, "dur": 16, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683755, "dur": 13, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683770, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683805, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683829, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683854, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683876, "dur": 17, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683895, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683918, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683935, "dur": 13, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683951, "dur": 16, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545683971, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684019, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684037, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684055, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684075, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684094, "dur": 16, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684113, "dur": 16, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684131, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684151, "dur": 13, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684167, "dur": 14, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684184, "dur": 38, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684224, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684238, "dur": 13, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684254, "dur": 16, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684272, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684292, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684311, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684331, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684354, "dur": 14, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684371, "dur": 14, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684387, "dur": 13, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684402, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684442, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684460, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684478, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684498, "dur": 15, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684516, "dur": 18, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684537, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684555, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684574, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684591, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684637, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684658, "dur": 78, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684740, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545684765, "dur": 374, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545685142, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545685193, "dur": 8, "ph": "X", "name": "ProcessMessages 1360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545685203, "dur": 20, "ph": "X", "name": "ReadAsync 1360", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545685225, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545685485, "dur": 754, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545686241, "dur": 190, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20752, "tid": 12884901888, "ts": 1753724545686433, "dur": 3536, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547844913, "dur": 1276, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20752, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20752, "tid": 8589934592, "ts": 1753724545595914, "dur": 63127, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20752, "tid": 8589934592, "ts": 1753724545659044, "dur": 5365, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20752, "tid": 8589934592, "ts": 1753724545664411, "dur": 1844, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547846191, "dur": 51, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20752, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724545583921, "dur": 107225, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724545587062, "dur": 5479, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724545691278, "dur": 531062, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724546222558, "dur": 1610841, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724546222689, "dur": 1460, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724547833413, "dur": 3383, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724547835788, "dur": 32, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20752, "tid": 4294967296, "ts": 1753724547836799, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547846245, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753724546240403, "dur": 126274, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724546366682, "dur": 312, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724546367094, "dur": 63, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753724546367157, "dur": 284, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724546373267, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753724546367458, "dur": 15142, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724546382614, "dur": 1446071, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724547828687, "dur": 291, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724547829153, "dur": 1108, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753724546367646, "dur": 14973, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546382621, "dur": 3220, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546385842, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546386044, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546386238, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546386422, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546386671, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546386905, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546387145, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546387658, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546387866, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546388066, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546388266, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546388487, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546388680, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546388907, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546389078, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546389438, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546389575, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546389705, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546390041, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546390474, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546389831, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546390795, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546390934, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546391087, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546391228, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546391589, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546391668, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546391994, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546392128, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546392350, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546392441, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546392528, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546392643, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546392765, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546393129, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546393208, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546393659, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546393962, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546394412, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753724546394507, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546394963, "dur": 43106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546438071, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546439734, "dur": 1721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546442740, "dur": 1122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546441490, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546444008, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546444319, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546444449, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546444098, "dur": 1783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546445882, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546447045, "dur": 801, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Web.HttpUtility.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546448442, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753724546446067, "dur": 2522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753724546448848, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.Linq.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753724546449011, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724546449188, "dur": 1379513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546367677, "dur": 14954, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546383004, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753724546383003, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546384174, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6146630326014295711.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753724546384371, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546384544, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546384758, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546384986, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546385354, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546385559, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546385774, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546385977, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546386174, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546386355, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546387147, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546387670, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546387881, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546388094, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546388289, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546388493, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546388949, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546389065, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546389440, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546389570, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546389702, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546389804, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546390189, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753724546390277, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753724546389864, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546390999, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546391108, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546391265, "dur": 541, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753724546391200, "dur": 1125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546392325, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546392469, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546392710, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRLabs.AV3Manager.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546392844, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546393375, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546393853, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546394252, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546394356, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724546394513, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546394584, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546395017, "dur": 43065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546438085, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546439690, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546439760, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546441177, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546441234, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546442707, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546442787, "dur": 1544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546444360, "dur": 1500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546445894, "dur": 1760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753724546447654, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546448007, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546448389, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546448605, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724546448853, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/VRC.SDK3A.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753724546449107, "dur": 1170284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724547619393, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753724547619393, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753724547619552, "dur": 1029, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SkinnedMeshTools.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753724547620585, "dur": 208099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546367670, "dur": 14954, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546382894, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724546382892, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_38B99B8F076E9E13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753724546383007, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724546383006, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_917E5BD489C19DA6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753724546383852, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546384369, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546384553, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546384773, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546385025, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546385436, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546385654, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546385888, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546386115, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546386500, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546386858, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546387069, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546387784, "dur": 772, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Runtime\\Core\\SelectionPickerRenderer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753724546387594, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546388556, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546388868, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546389074, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546389432, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753724546389942, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724546389564, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546390289, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753724546390609, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724546390422, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546391208, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546391673, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546392175, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546392498, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724546392489, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546393244, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546393736, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724546394243, "dur": 2722, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SkinnedMeshTools.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753724546393824, "dur": 3142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724546397321, "dur": 930968, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753724547330033, "dur": 200448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724547330031, "dur": 201559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SkinnedMeshTools.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753724547532469, "dur": 123, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724547532622, "dur": 81474, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SkinnedMeshTools.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753724547619390, "dur": 208618, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724547619389, "dur": 208621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753724547828031, "dur": 589, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SkinnedMeshTools.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546367698, "dur": 14938, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546382638, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753724546383009, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546383569, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753724546383702, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753724546384370, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546384535, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546384788, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546384999, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546385355, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546385559, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546385774, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546385971, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546386162, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546386570, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546387060, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546387571, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546387779, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546388001, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546388195, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546388400, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546388589, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546388867, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546389061, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546389432, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753724546389967, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Editor\\VRCSDK\\Plugins\\VRC.SDK3.Dynamics.Constraint.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546389566, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546390091, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546390397, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546390454, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753724546390608, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546390924, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546390595, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546391528, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546391612, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753724546391717, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546392258, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546391803, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546392586, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546392730, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546392709, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546393289, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546393374, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546394254, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\nadena.dev.ndmf\\Editor\\API\\Serialization\\AssetSaver.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753724546393372, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546394488, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546394613, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546395045, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546395366, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546395590, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546395808, "dur": 42279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546438089, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546439743, "dur": 1528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546441354, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546441311, "dur": 1513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546442824, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724546442976, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546444487, "dur": 1503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546446019, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546448290, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.ClientFactory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546448513, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724546447577, "dur": 1844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753724546449468, "dur": 1379191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546367724, "dur": 14917, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546382643, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724546382970, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546382969, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_942F6861D953A834.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724546384370, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546384554, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546384764, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546384964, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546385321, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546385522, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546385797, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\UniTask\\Runtime\\Linq\\Where.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753724546385730, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546386583, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546386834, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546387046, "dur": 756, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\Editor\\EditorCore\\DrawShapeTool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753724546387046, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546388001, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546388196, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546388404, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546388600, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546388714, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546388865, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546389055, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546389432, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724546389952, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Dependencies\\librsync\\Blake2Sharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546390275, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Dependencies\\Managed\\System.Collections.Immutable.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546389553, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546391265, "dur": 537, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546390768, "dur": 1713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546392523, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546392742, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724546392936, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546393440, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546393769, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724546394045, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546394206, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546394716, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546394945, "dur": 43123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546439746, "dur": 1505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546438070, "dur": 3191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.BuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546441354, "dur": 1343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546441315, "dur": 2979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546444343, "dur": 1554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.param-introspection.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546447036, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546447178, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724546445927, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.vrchat.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753724546448023, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546448246, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546448404, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546448488, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546448632, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546448848, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.KdTree.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753724546449013, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724546449245, "dur": 1379417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546367741, "dur": 14904, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546382648, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546383015, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1753724546382998, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_955A791FD004EFEC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546383148, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546384349, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546384662, "dur": 1154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546386977, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\ITestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753724546387160, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.29\\UnityEngine.TestRunner\\Utils\\IPrebuildSceneSetup.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753724546383277, "dur": 4401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546387736, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546387885, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546389072, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546389137, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546389427, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546389696, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546389534, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546390436, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546390608, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546390819, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546390995, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546390592, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546391391, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546391460, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546391575, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724546391892, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546392032, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546391693, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546392581, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546393139, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546393702, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546394240, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546394825, "dur": 43270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546438097, "dur": 1596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546439728, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546441239, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724546441140, "dur": 1641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546442809, "dur": 1539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546444358, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546445008, "dur": 1593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546446602, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546446773, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.ndmf.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753724546448858, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546449034, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724546449677, "dur": 1379026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546367760, "dur": 14890, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546382663, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546382652, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D2E6889350BDFFF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724546383011, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546383010, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8C5E2A3FA2C2B638.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724546384343, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546384531, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546384733, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546384938, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546385390, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546385631, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546386090, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546386309, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546386679, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546386873, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546387084, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546387605, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546387852, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546388054, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546388244, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546388454, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546388665, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546388887, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546389057, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546389428, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724546389693, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546389952, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546390042, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546389551, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546390618, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724546390926, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546391122, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724546391508, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@2.1.4\\Unity.Collections\\NativeRingQueue.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753724546390726, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546391779, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546391939, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724546392099, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546392640, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1753724546393115, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546393391, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546393513, "dur": 42856, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1753724546438066, "dur": 1634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546439730, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546441135, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546442547, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546442817, "dur": 1467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546444319, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546445632, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546445876, "dur": 1583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546447488, "dur": 1487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753724546449020, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724546449469, "dur": 1379227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546367780, "dur": 14875, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546382658, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_DD36A0C7D3C71D14.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546382717, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546383012, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546383441, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753724546383526, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.vrchat.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753724546383916, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753724546384809, "dur": 465, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SkinnedMeshTools.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753724546385322, "dur": 880, "ph": "X", "name": "File", "args": {"detail": "Packages\\vrchat.blackstartx.gesture-manager\\Scripts\\Editor\\Modules\\Vrc3\\OpenSoundControl\\VisualElements\\VisualEpStyles.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753724546385275, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546386663, "dur": 1137, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753724546386351, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546387850, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546388042, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546388230, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546388429, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546388644, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546388861, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546389058, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546389443, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546389588, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546390030, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546390105, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546390691, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDK3A.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546390784, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546390895, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546391122, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753724546391039, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546391790, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546391975, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546392165, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546392364, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546392460, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546392618, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.harmony-patches.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724546392726, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546393225, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546393705, "dur": 821, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546394607, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546394975, "dur": 43116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546438093, "dur": 1607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Oculus.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546439747, "dur": 1529, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753724546439732, "dur": 2959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546442732, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546444371, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546444583, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.core.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546446160, "dur": 1285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724546448639, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753724546448718, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753724546447452, "dur": 1877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.ExampleCentral.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753724546449381, "dur": 1379337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546367803, "dur": 14857, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546382959, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_E4C1498A702241CA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546383025, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753724546383023, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_5A104ACC7C39F2CD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546383785, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753724546384378, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546384722, "dur": 1092, "ph": "X", "name": "File", "args": {"detail": "Packages\\nadena.dev.ndmf\\Editor\\UI\\Localization\\NDMFLocales.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753724546384565, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546385863, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546386052, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546386243, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546386425, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546386612, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546386818, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546387015, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546387532, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546387762, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546387996, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546388190, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546388409, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546388632, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546388685, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546388890, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546389056, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546389435, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/lyuma.av3emulator.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546389560, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546389738, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546390399, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546390827, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546390984, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546391600, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546391728, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546391926, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546391985, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546392108, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546392737, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546393202, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546393326, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546393768, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546393860, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546394370, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546394442, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753724546394580, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546395042, "dur": 43030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546438075, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546439738, "dur": 1471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/FbxBuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546441250, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546442734, "dur": 1431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546444165, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724546444514, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546446045, "dur": 1636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.jordo.easyquestswitch.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546447925, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753724546448512, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753724546448982, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753724546447716, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.vrchat.core.vpm-resolver.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753724546449676, "dur": 1379032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546367820, "dur": 14845, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546382756, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_6CDB56D6AEFB4AFA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546383009, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546383268, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546383324, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753724546384247, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753724546384343, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546384537, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546384738, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546384935, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546385290, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546385491, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546385681, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546385879, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546386069, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546386255, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546386448, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546386768, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546386981, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546387505, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546387788, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.xr.management@4.4.0\\Editor\\TypeLoaderExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753724546387706, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546388662, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546388914, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546389059, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546389565, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.ndmf.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546389686, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546389829, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546390396, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546390535, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546390926, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753724546390659, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Oculus.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546391325, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546391453, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.param-introspection.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546391550, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546391656, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546391780, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546391944, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546392116, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546392235, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546392347, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.ShaderStripping.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546392525, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546392624, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724546392735, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546393120, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.SDKBase.Editor.BuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546393626, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546394126, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546394204, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546394789, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546395019, "dur": 43324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546438344, "dur": 1583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDK3A.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546439956, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546442196, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753724546441378, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546443154, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546445885, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753724546444709, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546446449, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724546448059, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Abstractions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753724546448513, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753724546447070, "dur": 2119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRLabs.AV3Manager.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753724546449241, "dur": 1379422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546367836, "dur": 14836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546382680, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C24F50ECF6FDA5D8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724546382864, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6AF695C02A28C358.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724546383003, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546383001, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_48F6D28C1C558B25.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724546383647, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/nadena.dev.modular-avatar.core.editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753724546384372, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546384556, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546384765, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546385312, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546385529, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546385804, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\UniTask\\Runtime\\Linq\\Subscribe.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753724546385747, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546386489, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546386892, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546387118, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546387617, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546387811, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546388014, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546388202, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546388402, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546388652, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546388979, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546389056, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546389435, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724546389562, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546390043, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546389560, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546390420, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546391074, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724546391194, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724546391334, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546391797, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546391434, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546392020, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546392297, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.blackstartx.gesture-manager.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546392969, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546393161, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/vrchat.jordo.easyquestswitch.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546393617, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546393708, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.vrchat.core.vpm-resolver.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546394188, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546394171, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546394799, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546395040, "dur": 43038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546438080, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546441238, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546441354, "dur": 1398, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546439742, "dur": 3426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546443199, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/lyuma.av3emulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546444707, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546447840, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Packages\\com.vrchat.base\\Runtime\\VRCSDK\\Plugins\\VRC.Dynamics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753724546446281, "dur": 1704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753724546447985, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546448338, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546448695, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546449014, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724546449388, "dur": 1379272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546367862, "dur": 14816, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546382815, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4864E34B5BFB8A03.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753724546383009, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546383007, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F97977208FF60E8C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753724546383163, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753724546384132, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546384346, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546385056, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546385805, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753724546383322, "dur": 3434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546386817, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546387014, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546387521, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546387712, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546387928, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546388126, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546388322, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546388523, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546388860, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546389062, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546389441, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753724546390043, "dur": 528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.probuilder@5.2.4\\External\\Poly2Tri\\Triangulation\\Util\\PolygonGenerator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753724546389555, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546390580, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546390786, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753724546391126, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546391051, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546391892, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546392010, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753724546392358, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546392176, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546392988, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546393385, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VRC.ExampleCentral.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546393817, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546393870, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546394296, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546394830, "dur": 43236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546438069, "dur": 1717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.XR.Management.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546439787, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546440015, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546439853, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546442249, "dur": 1483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/vrchat.blackstartx.gesture-manager.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546445026, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546443767, "dur": 1660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VRC.SDKBase.Editor.ShaderStripping.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546445428, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724546445587, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/nadena.dev.modular-avatar.harmony-patches.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546447841, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpOverrides.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546448982, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753724546447139, "dur": 1996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753724546449176, "dur": 1379526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724547832701, "dur": 1476, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1753724545757269, "dur": 445200, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1753724545758272, "dur": 57562, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1753724546120134, "dur": 5426, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1753724546125566, "dur": 76888, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1753724546127466, "dur": 51999, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1753724546208612, "dur": 1036, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1753724546208248, "dur": 1603, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753724545663512, "dur": 1189, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545664710, "dur": 735, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545665550, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753724545665608, "dur": 269, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545666889, "dur": 1425, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_DDB1499AED0A17A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753724545669309, "dur": 831, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753724545665896, "dur": 19820, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545685731, "dur": 437, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545686169, "dur": 166, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545686551, "dur": 1112, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753724545666067, "dur": 19670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753724545685739, "dur": 426, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724545666114, "dur": 19643, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724545685759, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_25D3E79CFAA66401.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753724545685820, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753724545686132, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724545666091, "dur": 19651, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753724545686129, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724545666109, "dur": 19642, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753724545685904, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724545685902, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D63E2E2896B4E5B3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753724545686134, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753724545686132, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F97977208FF60E8C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724545666133, "dur": 19629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753724545685764, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_278F9F6A3FFC9F4F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753724545686127, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753724545686125, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FF5E970CDD605B7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753724545666155, "dur": 19613, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753724545685783, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753724545685771, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_98D124CB06A9FE9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724545666173, "dur": 19602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753724545685778, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D2E6889350BDFFF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753724545686130, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753724545686128, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_917E5BD489C19DA6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753724545666195, "dur": 19586, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753724545686129, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724545666215, "dur": 19573, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753724545686127, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724545666231, "dur": 19563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753724545686048, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_38B99B8F076E9E13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753724545686141, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Unity versions\\Editor\\2022.3.22f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753724545686140, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8C5E2A3FA2C2B638.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753724545666252, "dur": 19548, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753724545686124, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724545666265, "dur": 19649, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753724545686129, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753724545690376, "dur": 318, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20752, "tid": 2307, "ts": 1753724547846830, "dur": 1709, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 20752, "tid": 2307, "ts": 1753724547850697, "dur": 42, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 20752, "tid": 2307, "ts": 1753724547850887, "dur": 20, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20752, "tid": 2307, "ts": 1753724547848582, "dur": 2111, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547850760, "dur": 126, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547850928, "dur": 132, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20752, "tid": 2307, "ts": 1753724547841368, "dur": 10415, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}